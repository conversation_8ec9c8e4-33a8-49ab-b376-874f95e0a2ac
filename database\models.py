import sqlite3
import os
from datetime import datetime
import bcrypt
from typing import Optional, List, Dict, Any

class DatabaseManager:
    def __init__(self, db_path: str = "database/payroll.db"):
        self.db_path = db_path
        self.ensure_database_exists()
        self.create_tables()
        self.create_default_admin()
    
    def ensure_database_exists(self):
        """Ensure the database directory exists"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
    
    def get_connection(self):
        """Get database connection"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def create_tables(self):
        """Create all necessary tables"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                role TEXT NOT NULL CHECK (role IN ('Admin', 'Supervisor', 'Regular User')),
                full_name TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        # Employees table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                full_name TEXT NOT NULL,
                nationality TEXT NOT NULL,
                id_residence_number TEXT UNIQUE NOT NULL,
                job_type TEXT NOT NULL,
                monthly_salary REAL NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NULL,
                additional_notes TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Payroll records table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS payroll_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                pay_period_month INTEGER NOT NULL,
                pay_period_year INTEGER NOT NULL,
                base_salary REAL NOT NULL,
                overtime_hours REAL DEFAULT 0,
                overtime_rate REAL DEFAULT 0,
                overtime_amount REAL DEFAULT 0,
                absence_days INTEGER DEFAULT 0,
                absence_deduction REAL DEFAULT 0,
                total_amount REAL NOT NULL,
                payment_status TEXT DEFAULT 'Unpaid' CHECK (payment_status IN ('Paid', 'Unpaid')),
                payment_date DATE NULL,
                payment_method TEXT NULL,
                paying_officer TEXT NULL,
                receipt_number TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id),
                UNIQUE(employee_id, pay_period_month, pay_period_year)
            )
        ''')
        
        # Attendance records table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS attendance_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                date DATE NOT NULL,
                status TEXT NOT NULL CHECK (status IN ('Present', 'Absent', 'Leave', 'Holiday')),
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id),
                UNIQUE(employee_id, date)
            )
        ''')
        
        # Leave requests table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS leave_requests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                leave_type TEXT NOT NULL,
                reason TEXT,
                status TEXT DEFAULT 'Pending' CHECK (status IN ('Pending', 'Approved', 'Rejected')),
                approved_by INTEGER NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id),
                FOREIGN KEY (approved_by) REFERENCES users (id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def create_default_admin(self):
        """Create default admin user if no users exist"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        
        if user_count == 0:
            password_hash = bcrypt.hashpw("admin123".encode('utf-8'), bcrypt.gensalt())
            cursor.execute('''
                INSERT INTO users (username, password_hash, role, full_name)
                VALUES (?, ?, ?, ?)
            ''', ("admin", password_hash.decode('utf-8'), "Admin", "System Administrator"))
            conn.commit()
        
        conn.close()

class UserModel:
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def authenticate(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """Authenticate user and return user data if successful"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, password_hash, role, full_name, is_active
            FROM users WHERE username = ? AND is_active = 1
        ''', (username,))
        
        user = cursor.fetchone()
        conn.close()
        
        if user and bcrypt.checkpw(password.encode('utf-8'), user['password_hash'].encode('utf-8')):
            return dict(user)
        return None
    
    def create_user(self, username: str, password: str, role: str, full_name: str) -> bool:
        """Create a new user"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
            cursor.execute('''
                INSERT INTO users (username, password_hash, role, full_name)
                VALUES (?, ?, ?, ?)
            ''', (username, password_hash.decode('utf-8'), role, full_name))
            
            conn.commit()
            conn.close()
            return True
        except sqlite3.IntegrityError:
            return False
    
    def get_all_users(self) -> List[Dict[str, Any]]:
        """Get all active users"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, role, full_name, created_at, is_active
            FROM users WHERE is_active = 1
            ORDER BY full_name
        ''')
        
        users = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return users

class EmployeeModel:
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def create_employee(self, employee_data: Dict[str, Any]) -> bool:
        """Create a new employee"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO employees (full_name, nationality, id_residence_number, 
                                     job_type, monthly_salary, start_date, additional_notes)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                employee_data['full_name'],
                employee_data['nationality'],
                employee_data['id_residence_number'],
                employee_data['job_type'],
                employee_data['monthly_salary'],
                employee_data['start_date'],
                employee_data.get('additional_notes', '')
            ))
            
            conn.commit()
            conn.close()
            return True
        except sqlite3.IntegrityError:
            return False
    
    def get_all_employees(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """Get all employees"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        query = '''
            SELECT id, full_name, nationality, id_residence_number, job_type,
                   monthly_salary, start_date, end_date, additional_notes, is_active
            FROM employees
        '''
        
        if active_only:
            query += " WHERE is_active = 1"
        
        query += " ORDER BY full_name"
        
        cursor.execute(query)
        employees = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return employees
    
    def get_employee_by_id(self, employee_id: int) -> Optional[Dict[str, Any]]:
        """Get employee by ID"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM employees WHERE id = ?
        ''', (employee_id,))
        
        employee = cursor.fetchone()
        conn.close()
        
        return dict(employee) if employee else None
    
    def update_employee(self, employee_id: int, employee_data: Dict[str, Any]) -> bool:
        """Update employee information"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE employees 
                SET full_name = ?, nationality = ?, id_residence_number = ?,
                    job_type = ?, monthly_salary = ?, start_date = ?,
                    additional_notes = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (
                employee_data['full_name'],
                employee_data['nationality'],
                employee_data['id_residence_number'],
                employee_data['job_type'],
                employee_data['monthly_salary'],
                employee_data['start_date'],
                employee_data.get('additional_notes', ''),
                employee_id
            ))
            
            conn.commit()
            conn.close()
            return True
        except sqlite3.IntegrityError:
            return False
    
    def deactivate_employee(self, employee_id: int) -> bool:
        """Deactivate an employee"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE employees 
            SET is_active = 0, end_date = CURRENT_DATE, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (employee_id,))
        
        conn.commit()
        conn.close()
        return cursor.rowcount > 0

class PayrollModel:
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager

    def create_payroll_record(self, payroll_data: Dict[str, Any]) -> bool:
        """Create a new payroll record"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO payroll_records (
                    employee_id, pay_period_month, pay_period_year, base_salary,
                    overtime_hours, overtime_rate, overtime_amount, absence_days,
                    absence_deduction, total_amount
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                payroll_data['employee_id'],
                payroll_data['pay_period_month'],
                payroll_data['pay_period_year'],
                payroll_data['base_salary'],
                payroll_data.get('overtime_hours', 0),
                payroll_data.get('overtime_rate', 0),
                payroll_data.get('overtime_amount', 0),
                payroll_data.get('absence_days', 0),
                payroll_data.get('absence_deduction', 0),
                payroll_data['total_amount']
            ))

            conn.commit()
            conn.close()
            return True
        except sqlite3.IntegrityError:
            return False

    def get_payroll_records(self, month: int = None, year: int = None,
                           employee_id: int = None) -> List[Dict[str, Any]]:
        """Get payroll records with optional filters"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        query = '''
            SELECT pr.*, e.full_name, e.job_type
            FROM payroll_records pr
            JOIN employees e ON pr.employee_id = e.id
            WHERE 1=1
        '''
        params = []

        if month:
            query += " AND pr.pay_period_month = ?"
            params.append(month)

        if year:
            query += " AND pr.pay_period_year = ?"
            params.append(year)

        if employee_id:
            query += " AND pr.employee_id = ?"
            params.append(employee_id)

        query += " ORDER BY pr.pay_period_year DESC, pr.pay_period_month DESC, e.full_name"

        cursor.execute(query, params)
        records = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return records

    def update_payment_status(self, record_id: int, payment_data: Dict[str, Any]) -> bool:
        """Update payment status and details"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            UPDATE payroll_records
            SET payment_status = 'Paid', payment_date = ?, payment_method = ?,
                paying_officer = ?, receipt_number = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (
            payment_data['payment_date'],
            payment_data['payment_method'],
            payment_data['paying_officer'],
            payment_data['receipt_number'],
            record_id
        ))

        conn.commit()
        conn.close()
        return cursor.rowcount > 0

    def get_payroll_record_by_id(self, record_id: int) -> Optional[Dict[str, Any]]:
        """Get payroll record by ID"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT pr.*, e.full_name, e.job_type, e.nationality, e.id_residence_number
            FROM payroll_records pr
            JOIN employees e ON pr.employee_id = e.id
            WHERE pr.id = ?
        ''', (record_id,))

        record = cursor.fetchone()
        conn.close()

        return dict(record) if record else None

class AttendanceModel:
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager

    def record_attendance(self, employee_id: int, date: str, status: str, notes: str = '') -> bool:
        """Record attendance for an employee"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO attendance_records (employee_id, date, status, notes)
                VALUES (?, ?, ?, ?)
            ''', (employee_id, date, status, notes))

            conn.commit()
            conn.close()
            return True
        except Exception:
            return False

    def get_attendance_records(self, employee_id: int = None, start_date: str = None,
                              end_date: str = None) -> List[Dict[str, Any]]:
        """Get attendance records with optional filters"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        query = '''
            SELECT ar.*, e.full_name
            FROM attendance_records ar
            JOIN employees e ON ar.employee_id = e.id
            WHERE 1=1
        '''
        params = []

        if employee_id:
            query += " AND ar.employee_id = ?"
            params.append(employee_id)

        if start_date:
            query += " AND ar.date >= ?"
            params.append(start_date)

        if end_date:
            query += " AND ar.date <= ?"
            params.append(end_date)

        query += " ORDER BY ar.date DESC, e.full_name"

        cursor.execute(query, params)
        records = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return records

    def get_absence_days(self, employee_id: int, month: int, year: int) -> int:
        """Get number of absence days for an employee in a specific month"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT COUNT(*) as absence_days
            FROM attendance_records
            WHERE employee_id = ?
            AND strftime('%m', date) = ?
            AND strftime('%Y', date) = ?
            AND status = 'Absent'
        ''', (employee_id, f"{month:02d}", str(year)))

        result = cursor.fetchone()
        conn.close()

        return result['absence_days'] if result else 0

class LeaveModel:
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager

    def create_leave_request(self, leave_data: Dict[str, Any]) -> bool:
        """Create a new leave request"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO leave_requests (employee_id, start_date, end_date,
                                          leave_type, reason)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                leave_data['employee_id'],
                leave_data['start_date'],
                leave_data['end_date'],
                leave_data['leave_type'],
                leave_data.get('reason', '')
            ))

            conn.commit()
            conn.close()
            return True
        except Exception:
            return False

    def get_leave_requests(self, employee_id: int = None, status: str = None) -> List[Dict[str, Any]]:
        """Get leave requests with optional filters"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        query = '''
            SELECT lr.*, e.full_name, u.full_name as approved_by_name
            FROM leave_requests lr
            JOIN employees e ON lr.employee_id = e.id
            LEFT JOIN users u ON lr.approved_by = u.id
            WHERE 1=1
        '''
        params = []

        if employee_id:
            query += " AND lr.employee_id = ?"
            params.append(employee_id)

        if status:
            query += " AND lr.status = ?"
            params.append(status)

        query += " ORDER BY lr.created_at DESC"

        cursor.execute(query, params)
        requests = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return requests

    def update_leave_status(self, request_id: int, status: str, approved_by: int = None) -> bool:
        """Update leave request status"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            UPDATE leave_requests
            SET status = ?, approved_by = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (status, approved_by, request_id))

        conn.commit()
        conn.close()
        return cursor.rowcount > 0
