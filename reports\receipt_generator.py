import os
from datetime import datetime
from reportlab.lib.pagesizes import A4, letter
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, mm
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from utils.helpers import Receipt<PERSON><PERSON>per, <PERSON>Helper
from typing import Dict, Any, Optional

class ReceiptGenerator:
    def __init__(self, output_dir: str = "receipts"):
        self.output_dir = output_dir
        FileHelper.ensure_directory_exists(output_dir)
        
        # Company information (can be configured)
        self.company_info = {
            'name': 'Domestic Worker Management Company',
            'address': 'P.O. Box 12345, Doha, Qatar',
            'phone': '+974 1234 5678',
            'email': '<EMAIL>',
            'website': 'www.dwmc.qa'
        }
        
        # Setup styles
        self.styles = getSampleStyleSheet()
        self.setup_custom_styles()
    
    def setup_custom_styles(self):
        """Setup custom paragraph styles"""
        # Company name style
        self.styles.add(ParagraphStyle(
            name='CompanyName',
            parent=self.styles['Heading1'],
            fontSize=18,
            spaceAfter=6,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        ))
        
        # Company details style
        self.styles.add(ParagraphStyle(
            name='CompanyDetails',
            parent=self.styles['Normal'],
            fontSize=10,
            alignment=TA_CENTER,
            spaceAfter=12
        ))
        
        # Receipt title style
        self.styles.add(ParagraphStyle(
            name='ReceiptTitle',
            parent=self.styles['Heading2'],
            fontSize=16,
            spaceAfter=12,
            alignment=TA_CENTER,
            textColor=colors.darkred
        ))
        
        # Section header style
        self.styles.add(ParagraphStyle(
            name='SectionHeader',
            parent=self.styles['Heading3'],
            fontSize=12,
            spaceAfter=6,
            textColor=colors.darkblue
        ))
        
        # Receipt info style
        self.styles.add(ParagraphStyle(
            name='ReceiptInfo',
            parent=self.styles['Normal'],
            fontSize=10,
            spaceAfter=3
        ))
        
        # Amount style
        self.styles.add(ParagraphStyle(
            name='Amount',
            parent=self.styles['Normal'],
            fontSize=14,
            alignment=TA_CENTER,
            textColor=colors.darkgreen,
            fontName='Helvetica-Bold'
        ))
        
        # Footer style
        self.styles.add(ParagraphStyle(
            name='Footer',
            parent=self.styles['Normal'],
            fontSize=8,
            alignment=TA_CENTER,
            textColor=colors.grey
        ))
    
    def generate_receipt(self, payroll_record: Dict[str, Any]) -> str:
        """Generate PDF receipt for a payroll record"""
        # Generate filename
        employee_name = FileHelper.clean_filename(payroll_record['full_name'])
        period = f"{payroll_record['pay_period_year']}{payroll_record['pay_period_month']:02d}"
        receipt_number = payroll_record.get('receipt_number', 'UNKNOWN')
        
        filename = f"receipt_{employee_name}_{period}_{receipt_number}.pdf"
        filepath = os.path.join(self.output_dir, filename)
        
        # Create PDF document
        doc = SimpleDocTemplate(
            filepath,
            pagesize=A4,
            rightMargin=20*mm,
            leftMargin=20*mm,
            topMargin=20*mm,
            bottomMargin=20*mm
        )
        
        # Build content
        story = []
        
        # Add company header
        self.add_company_header(story)
        
        # Add receipt title and info
        self.add_receipt_header(story, payroll_record)
        
        # Add employee information
        self.add_employee_info(story, payroll_record)
        
        # Add payment details
        self.add_payment_details(story, payroll_record)
        
        # Add amount section
        self.add_amount_section(story, payroll_record)
        
        # Add signature section
        self.add_signature_section(story, payroll_record)
        
        # Add footer
        self.add_footer(story)
        
        # Build PDF
        doc.build(story)
        
        return filepath
    
    def add_company_header(self, story):
        """Add company header to receipt"""
        # Company logo (if available)
        logo_path = "assets/logo.png"
        if os.path.exists(logo_path):
            try:
                logo = Image(logo_path, width=60*mm, height=30*mm)
                logo.hAlign = 'CENTER'
                story.append(logo)
                story.append(Spacer(1, 6*mm))
            except:
                pass  # Skip logo if there's an error
        
        # Company name
        story.append(Paragraph(self.company_info['name'], self.styles['CompanyName']))
        
        # Company details
        details = f"{self.company_info['address']}<br/>"
        details += f"Phone: {self.company_info['phone']} | Email: {self.company_info['email']}<br/>"
        details += f"Website: {self.company_info['website']}"
        
        story.append(Paragraph(details, self.styles['CompanyDetails']))
        
        # Separator line
        story.append(Spacer(1, 3*mm))
        line_table = Table([['_' * 80]], colWidths=[180*mm])
        line_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTSIZE', (0, 0), (-1, -1), 8),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.grey)
        ]))
        story.append(line_table)
        story.append(Spacer(1, 6*mm))
    
    def add_receipt_header(self, story, payroll_record):
        """Add receipt title and basic info"""
        # Receipt title
        story.append(Paragraph("SALARY PAYMENT RECEIPT", self.styles['ReceiptTitle']))
        
        # Receipt info table
        receipt_data = [
            ['Receipt Number:', payroll_record.get('receipt_number', 'N/A')],
            ['Date:', payroll_record.get('payment_date', datetime.now().strftime('%Y-%m-%d'))],
            ['Pay Period:', f"{payroll_record['pay_period_month']:02d}/{payroll_record['pay_period_year']}"]
        ]
        
        receipt_table = Table(receipt_data, colWidths=[40*mm, 60*mm])
        receipt_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
            ('ALIGN', (1, 0), (1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (1, 0), (1, -1), 10),
        ]))
        
        story.append(receipt_table)
        story.append(Spacer(1, 8*mm))
    
    def add_employee_info(self, story, payroll_record):
        """Add employee information section"""
        story.append(Paragraph("EMPLOYEE INFORMATION", self.styles['SectionHeader']))
        
        employee_data = [
            ['Employee Name:', payroll_record['full_name']],
            ['Nationality:', payroll_record.get('nationality', 'N/A')],
            ['ID/Residence No.:', payroll_record.get('id_residence_number', 'N/A')],
            ['Job Type:', payroll_record.get('job_type', 'N/A')]
        ]
        
        employee_table = Table(employee_data, colWidths=[50*mm, 100*mm])
        employee_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (1, 0), (1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
        ]))
        
        story.append(employee_table)
        story.append(Spacer(1, 8*mm))
    
    def add_payment_details(self, story, payroll_record):
        """Add payment breakdown section"""
        story.append(Paragraph("PAYMENT BREAKDOWN", self.styles['SectionHeader']))
        
        # Payment breakdown table
        breakdown_data = [
            ['Description', 'Amount (QAR)'],
            ['Base Salary', f"{payroll_record['base_salary']:,.2f}"],
        ]
        
        # Add overtime if applicable
        if payroll_record.get('overtime_hours', 0) > 0:
            breakdown_data.append([
                f"Overtime ({payroll_record.get('overtime_hours', 0)} hrs @ {payroll_record.get('overtime_rate', 0):.2f})",
                f"{payroll_record.get('overtime_amount', 0):,.2f}"
            ])
        
        # Add deductions if applicable
        if payroll_record.get('absence_days', 0) > 0:
            breakdown_data.append([
                f"Absence Deduction ({payroll_record.get('absence_days', 0)} days)",
                f"-{payroll_record.get('absence_deduction', 0):,.2f}"
            ])
        
        # Add total
        breakdown_data.append(['', ''])  # Empty row for spacing
        breakdown_data.append(['TOTAL AMOUNT', f"{payroll_record['total_amount']:,.2f}"])
        
        breakdown_table = Table(breakdown_data, colWidths=[120*mm, 50*mm])
        breakdown_table.setStyle(TableStyle([
            # Header row
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('ALIGN', (0, 0), (0, 0), 'LEFT'),
            ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
            
            # Data rows
            ('FONTSIZE', (0, 1), (-1, -3), 10),
            ('ALIGN', (0, 1), (0, -3), 'LEFT'),
            ('ALIGN', (1, 1), (1, -3), 'RIGHT'),
            
            # Total row
            ('BACKGROUND', (0, -1), (-1, -1), colors.lightblue),
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, -1), (-1, -1), 12),
            ('ALIGN', (0, -1), (0, -1), 'LEFT'),
            ('ALIGN', (1, -1), (1, -1), 'RIGHT'),
            
            # General styling
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -3), 1, colors.black),
            ('GRID', (0, -1), (-1, -1), 2, colors.black),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('RIGHTPADDING', (0, 0), (-1, -1), 6),
            ('TOPPADDING', (0, 0), (-1, -1), 4),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
        ]))
        
        story.append(breakdown_table)
        story.append(Spacer(1, 8*mm))
    
    def add_amount_section(self, story, payroll_record):
        """Add amount in words section"""
        amount = payroll_record['total_amount']
        amount_in_words = ReceiptHelper.number_to_words(amount)
        
        story.append(Paragraph("AMOUNT IN WORDS", self.styles['SectionHeader']))
        
        amount_text = f"{ReceiptHelper.format_currency(amount)}<br/>"
        amount_text += f"({amount_in_words} Qatari Riyals Only)"
        
        story.append(Paragraph(amount_text, self.styles['Amount']))
        story.append(Spacer(1, 10*mm))
    
    def add_signature_section(self, story, payroll_record):
        """Add signature section"""
        story.append(Paragraph("PAYMENT CONFIRMATION", self.styles['SectionHeader']))
        
        # Payment info
        payment_info = [
            ['Payment Method:', payroll_record.get('payment_method', 'N/A')],
            ['Paying Officer:', payroll_record.get('paying_officer', 'N/A')],
            ['Payment Date:', payroll_record.get('payment_date', 'N/A')]
        ]
        
        payment_table = Table(payment_info, colWidths=[50*mm, 100*mm])
        payment_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (1, 0), (1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
        ]))
        
        story.append(payment_table)
        story.append(Spacer(1, 15*mm))
        
        # Signature boxes
        signature_data = [
            ['Employee Signature:', '', 'Officer Signature:', ''],
            ['', '', '', ''],
            ['_' * 25, '', '_' * 25, ''],
            [payroll_record['full_name'], '', payroll_record.get('paying_officer', ''), ''],
            ['Employee', '', 'Paying Officer', '']
        ]
        
        signature_table = Table(signature_data, colWidths=[60*mm, 20*mm, 60*mm, 20*mm])
        signature_table.setStyle(TableStyle([
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('FONTNAME', (0, 3), (0, 3), 'Helvetica-Bold'),
            ('FONTNAME', (2, 3), (2, 3), 'Helvetica-Bold'),
            ('FONTNAME', (0, 4), (0, 4), 'Helvetica-Oblique'),
            ('FONTNAME', (2, 4), (2, 4), 'Helvetica-Oblique'),
            ('FONTSIZE', (0, 4), (-1, 4), 8),
        ]))
        
        story.append(signature_table)
        story.append(Spacer(1, 10*mm))
    
    def add_footer(self, story):
        """Add footer to receipt"""
        footer_text = f"This is a computer-generated receipt. Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}<br/>"
        footer_text += "For any queries, please contact the HR department."
        
        story.append(Paragraph(footer_text, self.styles['Footer']))
    
    def generate_batch_receipts(self, payroll_records: list) -> list:
        """Generate receipts for multiple payroll records"""
        generated_files = []
        errors = []
        
        for record in payroll_records:
            try:
                if record.get('payment_status') == 'Paid':
                    filepath = self.generate_receipt(record)
                    generated_files.append(filepath)
            except Exception as e:
                errors.append(f"Error generating receipt for {record.get('full_name', 'Unknown')}: {str(e)}")
        
        return generated_files, errors
