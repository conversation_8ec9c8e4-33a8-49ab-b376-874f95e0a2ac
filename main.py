#!/usr/bin/env python3
"""
Domestic Worker Payroll Management System
Main Application Entry Point

This is the main entry point for the Domestic Worker Payroll Management System.
It initializes the database, sets up the main application window, and handles
the overall application lifecycle.

Author: AI Assistant
Date: 2024
Version: 1.0
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import application modules
try:
    from database.models import DatabaseManager, UserModel, EmployeeModel, PayrollModel, AttendanceModel, LeaveModel
    from ui.login import SessionManager, LoginWindow
    from ui.employee_management import EmployeeManagementWindow
    from ui.payroll import PayrollProcessingWindow
    from ui.reports import ReportsWindow
    from ui.attendance import AttendanceManagementWindow
    from utils.backup import DatabaseBackup
    from utils.helpers import FileHelper
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Please ensure all required modules are installed and accessible.")
    sys.exit(1)

class MainApplication:
    """Main application class that manages the entire system"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Domestic Worker Payroll Management System")
        self.root.geometry("1200x800")

        # Hide main window initially until login
        self.root.withdraw()

        # Initialize database and models
        self.init_database()

        # Initialize session manager
        self.session_manager = SessionManager()

        # Set application icon (if available)
        try:
            icon_path = "assets/icon.ico"
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except:
            pass  # Ignore if icon not found

        # Create main interface (but keep it hidden)
        self.create_main_interface()

        # Handle window close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Show login window first
        self.show_login()
    
    def init_database(self):
        """Initialize database and create models"""
        try:
            # Ensure database directory exists
            FileHelper.ensure_directory_exists("database")
            
            # Initialize database manager
            self.db_manager = DatabaseManager()
            
            # Initialize models
            self.user_model = UserModel(self.db_manager)
            self.employee_model = EmployeeModel(self.db_manager)
            self.payroll_model = PayrollModel(self.db_manager)
            self.attendance_model = AttendanceModel(self.db_manager)
            self.leave_model = LeaveModel(self.db_manager)
            
            print("Database initialized successfully")
            
        except Exception as e:
            messagebox.showerror("Database Error", 
                               f"Failed to initialize database: {str(e)}")
            sys.exit(1)
    
    def create_main_interface(self):
        """Create the main application interface"""
        # Create menu bar
        self.create_menu_bar()
        
        # Create toolbar
        self.create_toolbar()
        
        # Create status bar
        self.create_status_bar()
        
        # Create main content area
        self.create_main_content()
        
        # Initially hide main interface (show after login)
        self.hide_main_interface()
    
    def create_menu_bar(self):
        """Create application menu bar"""
        self.menubar = tk.Menu(self.root)
        self.root.config(menu=self.menubar)
        
        # File menu
        file_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Backup Database", command=self.backup_database)
        file_menu.add_command(label="Restore Database", command=self.restore_database)
        file_menu.add_separator()
        file_menu.add_command(label="Export Data", command=self.export_data)
        file_menu.add_command(label="Import Data", command=self.import_data)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.on_closing)
        
        # Employee menu
        employee_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="Employees", menu=employee_menu)
        employee_menu.add_command(label="Manage Employees", command=self.open_employee_management)
        employee_menu.add_command(label="Add New Employee", command=self.add_new_employee)
        employee_menu.add_separator()
        employee_menu.add_command(label="Employee Reports", command=self.open_employee_reports)
        
        # Payroll menu
        payroll_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="Payroll", menu=payroll_menu)
        payroll_menu.add_command(label="Process Payroll", command=self.open_payroll_processing)
        payroll_menu.add_command(label="Payment History", command=self.open_payment_history)
        payroll_menu.add_command(label="Generate Receipts", command=self.generate_receipts)
        
        # Attendance menu
        attendance_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="Attendance", menu=attendance_menu)
        attendance_menu.add_command(label="Daily Attendance", command=self.open_attendance_management)
        attendance_menu.add_command(label="Leave Management", command=self.open_leave_management)
        attendance_menu.add_command(label="Attendance Reports", command=self.open_attendance_reports)
        
        # Reports menu
        reports_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="Reports", menu=reports_menu)
        reports_menu.add_command(label="Financial Reports", command=self.open_financial_reports)
        reports_menu.add_command(label="Employee Reports", command=self.open_employee_reports)
        reports_menu.add_command(label="Payroll Reports", command=self.open_payroll_reports)
        
        # Tools menu
        tools_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="User Management", command=self.open_user_management)
        tools_menu.add_command(label="System Settings", command=self.open_settings)
        tools_menu.add_command(label="Database Maintenance", command=self.open_database_maintenance)
        
        # Help menu
        help_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="User Manual", command=self.show_user_manual)
        help_menu.add_command(label="About", command=self.show_about)
    
    def create_toolbar(self):
        """Create application toolbar"""
        self.toolbar = ttk.Frame(self.root)
        self.toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)
        
        # Quick access buttons
        ttk.Button(self.toolbar, text="Employees", command=self.open_employee_management).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="Payroll", command=self.open_payroll_processing).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="Attendance", command=self.open_attendance_management).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="Reports", command=self.open_reports).pack(side=tk.LEFT, padx=2)
        
        # Separator
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        # User info
        self.user_info_label = ttk.Label(self.toolbar, text="Not logged in")
        self.user_info_label.pack(side=tk.RIGHT, padx=10)
        
        ttk.Button(self.toolbar, text="Logout", command=self.logout).pack(side=tk.RIGHT, padx=2)
    
    def create_status_bar(self):
        """Create application status bar"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_label = ttk.Label(self.status_bar, text="Ready")
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        # Database status
        self.db_status_label = ttk.Label(self.status_bar, text="Database: Connected")
        self.db_status_label.pack(side=tk.RIGHT, padx=5)
    
    def create_main_content(self):
        """Create main content area"""
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Welcome message
        welcome_frame = ttk.LabelFrame(self.main_frame, text="Welcome", padding="20")
        welcome_frame.pack(fill=tk.BOTH, expand=True)
        
        welcome_text = """
Welcome to the Domestic Worker Payroll Management System

This system helps you manage:
• Employee information and records
• Monthly payroll processing and payments
• Attendance tracking and leave management
• Financial reports and analytics
• Receipt generation and document management

Quick Start:
1. Use the toolbar buttons for quick access to main features
2. Navigate through the menu for detailed options
3. All data is automatically saved to the database
4. Use the Reports section for comprehensive analytics

For help and support, check the Help menu or contact your system administrator.
        """
        
        welcome_label = ttk.Label(welcome_frame, text=welcome_text, font=('Arial', 11))
        welcome_label.pack(anchor=tk.W)
        
        # Quick stats frame (will be populated after login)
        self.stats_frame = ttk.LabelFrame(self.main_frame, text="Quick Statistics", padding="10")
        self.stats_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.stats_text = ttk.Label(self.stats_frame, text="Login to view statistics")
        self.stats_text.pack()
    
    def hide_main_interface(self):
        """Hide main interface elements"""
        self.menubar.entryconfig("Employees", state="disabled")
        self.menubar.entryconfig("Payroll", state="disabled")
        self.menubar.entryconfig("Attendance", state="disabled")
        self.menubar.entryconfig("Reports", state="disabled")
        self.menubar.entryconfig("Tools", state="disabled")
        
        for widget in self.toolbar.winfo_children():
            if isinstance(widget, ttk.Button) and widget['text'] != 'Logout':
                widget.config(state='disabled')
    
    def show_main_interface(self):
        """Show main interface elements after successful login"""
        self.menubar.entryconfig("Employees", state="normal")
        self.menubar.entryconfig("Payroll", state="normal")
        self.menubar.entryconfig("Attendance", state="normal")
        self.menubar.entryconfig("Reports", state="normal")
        self.menubar.entryconfig("Tools", state="normal")
        
        for widget in self.toolbar.winfo_children():
            if isinstance(widget, ttk.Button):
                widget.config(state='normal')
        
        # Update user info
        if self.session_manager.current_user:
            user_info = f"Logged in as: {self.session_manager.current_user['full_name']} ({self.session_manager.current_user['role']})"
            self.user_info_label.config(text=user_info)
        
        # Update quick statistics
        self.update_quick_stats()
    
    def show_login(self):
        """Show login window"""
        try:
            # Create and show login window
            login_window = LoginWindow(self.user_model, self.session_manager,
                                     on_success_callback=self.on_login_success)
            login_window.show()

            # Check if login was successful after window closes
            if not self.session_manager.is_authenticated():
                # Exit if login was cancelled or failed
                self.root.quit()

        except Exception as e:
            messagebox.showerror("Login Error", f"Error showing login window: {str(e)}")
            self.root.quit()

    def on_login_success(self):
        """Called when login is successful"""
        self.show_main_interface()
        self.set_status("Login successful")
    
    def logout(self):
        """Logout current user"""
        if messagebox.askyesno("Logout", "Are you sure you want to logout?"):
            self.session_manager.logout()
            self.hide_main_interface()
            self.user_info_label.config(text="Not logged in")
            self.stats_text.config(text="Login to view statistics")
            self.show_login()
    
    def update_quick_stats(self):
        """Update quick statistics display"""
        try:
            # Get basic statistics
            total_employees = len(self.employee_model.get_all_employees())
            active_employees = len(self.employee_model.get_all_employees(active_only=True))
            
            # Get current month payroll info
            from datetime import datetime
            now = datetime.now()
            current_payroll = self.payroll_model.get_payroll_records(month=now.month, year=now.year)
            paid_count = len([r for r in current_payroll if r['payment_status'] == 'Paid'])
            unpaid_count = len([r for r in current_payroll if r['payment_status'] == 'Unpaid'])
            
            stats_text = f"Total Employees: {total_employees} | Active: {active_employees} | "
            stats_text += f"This Month - Paid: {paid_count}, Unpaid: {unpaid_count}"
            
            self.stats_text.config(text=stats_text)
        
        except Exception as e:
            self.stats_text.config(text="Error loading statistics")
    
    def set_status(self, message):
        """Set status bar message"""
        self.status_label.config(text=message)
        self.root.after(5000, lambda: self.status_label.config(text="Ready"))  # Clear after 5 seconds
    
    # Menu command methods
    def open_employee_management(self):
        """Open employee management window"""
        EmployeeManagementWindow(self.root, self.employee_model, self.session_manager)
    
    def add_new_employee(self):
        """Open new employee dialog"""
        # This would open the employee form in add mode
        self.open_employee_management()
    
    def open_payroll_processing(self):
        """Open payroll processing window"""
        PayrollProcessingWindow(self.root, self.employee_model, self.payroll_model, 
                               self.attendance_model, self.session_manager)
    
    def open_payment_history(self):
        """Open payment history in reports window"""
        reports_window = ReportsWindow(self.root, self.employee_model, self.payroll_model, 
                                     self.attendance_model, self.session_manager)
    
    def open_attendance_management(self):
        """Open attendance management window"""
        AttendanceManagementWindow(self.root, self.employee_model, self.attendance_model, 
                                 self.leave_model, self.session_manager)
    
    def open_leave_management(self):
        """Open leave management tab in attendance window"""
        self.open_attendance_management()
    
    def open_attendance_reports(self):
        """Open attendance reports"""
        self.open_attendance_management()
    
    def open_reports(self):
        """Open reports window"""
        ReportsWindow(self.root, self.employee_model, self.payroll_model, 
                     self.attendance_model, self.session_manager)
    
    def open_financial_reports(self):
        """Open financial reports"""
        self.open_reports()
    
    def open_employee_reports(self):
        """Open employee reports"""
        self.open_reports()
    
    def open_payroll_reports(self):
        """Open payroll reports"""
        self.open_reports()
    
    def generate_receipts(self):
        """Generate receipts for paid salaries"""
        messagebox.showinfo("Generate Receipts", "Receipt generation would be implemented here")
    
    def open_user_management(self):
        """Open user management dialog"""
        if not self.session_manager.has_permission('Admin'):
            messagebox.showerror("Access Denied", "Administrator privileges required")
            return
        
        from ui.login import UserManagementDialog
        UserManagementDialog(self.root, self.user_model, self.session_manager)
    
    def open_settings(self):
        """Open system settings"""
        messagebox.showinfo("Settings", "System settings would be implemented here")
    
    def open_database_maintenance(self):
        """Open database maintenance tools"""
        messagebox.showinfo("Database Maintenance", "Database maintenance tools would be implemented here")
    
    def backup_database(self):
        """Backup database"""
        try:
            backup = DatabaseBackup()
            backup_path = backup.create_backup()
            messagebox.showinfo("Backup Complete", f"Database backed up to: {backup_path}")
        except Exception as e:
            messagebox.showerror("Backup Error", f"Failed to backup database: {str(e)}")
    
    def restore_database(self):
        """Restore database from backup"""
        messagebox.showinfo("Restore Database", "Database restore functionality would be implemented here")
    
    def export_data(self):
        """Export data to Excel"""
        messagebox.showinfo("Export Data", "Data export functionality would be implemented here")
    
    def import_data(self):
        """Import data from Excel"""
        messagebox.showinfo("Import Data", "Data import functionality would be implemented here")
    
    def show_user_manual(self):
        """Show user manual"""
        messagebox.showinfo("User Manual", "User manual would be implemented here")
    
    def show_about(self):
        """Show about dialog"""
        about_text = """
Domestic Worker Payroll Management System
Version 1.0

A comprehensive solution for managing domestic worker payroll,
attendance, and administrative tasks.

Features:
• Employee management and records
• Automated payroll processing
• Attendance and leave tracking
• Financial reporting and analytics
• Receipt generation and printing
• Data backup and security

Developed with Python and tkinter
© 2024 - All rights reserved
        """
        messagebox.showinfo("About", about_text)
    
    def on_closing(self):
        """Handle application closing"""
        if messagebox.askokcancel("Quit", "Do you want to quit the application?"):
            # Cleanup and close
            if hasattr(self, 'db_manager'):
                self.db_manager.close()
            self.root.destroy()
    
    def run(self):
        """Start the application"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("Application interrupted by user")
        except Exception as e:
            print(f"Application error: {str(e)}")
            messagebox.showerror("Application Error", f"An unexpected error occurred: {str(e)}")
        finally:
            # Cleanup
            if hasattr(self, 'db_manager'):
                try:
                    self.db_manager.close()
                except:
                    pass

def main():
    """Main function to start the application"""
    try:
        # Create and run the application
        app = MainApplication()
        app.run()
    
    except Exception as e:
        print(f"Failed to start application: {e}")
        messagebox.showerror("Startup Error", f"Failed to start application: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
