#!/usr/bin/env python3
"""
Simple test to check if Python and tkinter are working
"""

import sys
import os

print("Python version:", sys.version)
print("Current directory:", os.getcwd())
print("Python path:", sys.path[0])

try:
    import tkinter as tk
    print("✓ tkinter imported successfully")
    
    # Test basic tkinter window
    root = tk.Tk()
    root.title("Test Window")
    root.geometry("300x200")
    
    label = tk.Label(root, text="Test Window - النظام يعمل!")
    label.pack(pady=50)
    
    button = tk.Button(root, text="إغلاق", command=root.destroy)
    button.pack()
    
    print("✓ Test window created successfully")
    print("✓ Starting tkinter mainloop...")
    
    root.mainloop()
    print("✓ Window closed successfully")
    
except ImportError as e:
    print(f"✗ Error importing tkinter: {e}")
    sys.exit(1)
except Exception as e:
    print(f"✗ Error creating window: {e}")
    sys.exit(1)

print("Test completed successfully!")
