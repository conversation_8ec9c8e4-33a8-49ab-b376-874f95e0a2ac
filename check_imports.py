#!/usr/bin/env python3
"""
Check if all required modules can be imported
"""

import sys
import os

def check_import(module_name, description=""):
    try:
        __import__(module_name)
        print(f"✓ {module_name} - {description}")
        return True
    except ImportError as e:
        print(f"✗ {module_name} - {description} (خطأ: {e})")
        return False

def main():
    print("=" * 60)
    print("فحص المكتبات المطلوبة")
    print("Checking Required Libraries")
    print("=" * 60)
    
    print(f"Python Version: {sys.version}")
    print(f"Current Directory: {os.getcwd()}")
    print()
    
    # Check standard library modules
    print("📚 المكتبات الأساسية:")
    standard_libs = [
        ("tkinter", "واجهة المستخدم الرسومية"),
        ("sqlite3", "قاعدة البيانات"),
        ("datetime", "التاريخ والوقت"),
        ("pathlib", "إدارة المسارات"),
        ("typing", "تحديد الأنواع")
    ]
    
    all_standard = True
    for lib, desc in standard_libs:
        if not check_import(lib, desc):
            all_standard = False
    
    print()
    
    # Check third-party modules
    print("📦 المكتبات الخارجية:")
    third_party_libs = [
        ("bcrypt", "تشفير كلمات المرور"),
        ("pandas", "معالجة البيانات"),
        ("openpyxl", "ملفات Excel"),
        ("reportlab", "إنتاج ملفات PDF"),
        ("PIL", "معالجة الصور")
    ]
    
    all_third_party = True
    for lib, desc in third_party_libs:
        if not check_import(lib, desc):
            all_third_party = False
    
    print()
    
    # Check project modules
    print("🏗️ وحدات المشروع:")
    
    # Add current directory to path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    project_modules = [
        ("database.models", "نماذج قاعدة البيانات"),
        ("ui.login", "نافذة تسجيل الدخول"),
        ("ui.employee_management", "إدارة الموظفين"),
        ("ui.payroll", "معالجة الرواتب"),
        ("ui.reports", "التقارير"),
        ("ui.attendance", "الحضور والغياب"),
        ("utils.validators", "التحقق من البيانات"),
        ("utils.helpers", "الوظائف المساعدة"),
        ("utils.backup", "النسخ الاحتياطي"),
        ("reports.receipt_generator", "إنتاج الإيصالات")
    ]
    
    all_project = True
    for module, desc in project_modules:
        if not check_import(module, desc):
            all_project = False
    
    print()
    print("=" * 60)
    
    if all_standard and all_third_party and all_project:
        print("🎉 جميع المكتبات متوفرة! يمكن تشغيل النظام الآن.")
        print("🎉 All libraries available! System can run now.")
        print()
        print("لتشغيل النظام:")
        print("To run the system:")
        print("python run_system.py")
        print("أو انقر نقراً مزدوجاً على run_system.bat")
    else:
        print("❌ بعض المكتبات مفقودة!")
        print("❌ Some libraries are missing!")
        print()
        if not all_third_party:
            print("لتثبيت المكتبات المفقودة:")
            print("To install missing libraries:")
            print("pip install -r requirements.txt")
            print("أو انقر نقراً مزدوجاً على install_requirements.bat")
        
        if not all_project:
            print("تأكد من وجود جميع ملفات المشروع في المجلد الصحيح")
            print("Make sure all project files are in the correct directory")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
