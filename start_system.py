#!/usr/bin/env python3
"""
Simple system starter - نظام بدء التشغيل البسيط
"""

import sys
import os
import traceback

def main():
    print("🚀 بدء تشغيل نظام إدارة رواتب العمالة المنزلية")
    print("🚀 Starting Domestic Worker Payroll Management System")
    print("=" * 60)
    
    try:
        # Check Python version
        if sys.version_info < (3, 8):
            print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
            print("❌ Error: Python 3.8 or newer required")
            input("اضغط Enter للخروج...")
            return
        
        print(f"✓ Python {sys.version.split()[0]}")
        
        # Add current directory to Python path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        print(f"✓ مجلد العمل: {current_dir}")
        
        # Test basic imports
        print("🔄 فحص المكتبات الأساسية...")
        
        import tkinter as tk
        print("✓ tkinter")
        
        import sqlite3
        print("✓ sqlite3")
        
        try:
            import bcrypt
            print("✓ bcrypt")
        except ImportError:
            print("❌ bcrypt مفقود - جاري التثبيت...")
            os.system("pip install bcrypt")
            import bcrypt
            print("✓ bcrypt (تم التثبيت)")
        
        try:
            import reportlab
            print("✓ reportlab")
        except ImportError:
            print("❌ reportlab مفقود - جاري التثبيت...")
            os.system("pip install reportlab")
            import reportlab
            print("✓ reportlab (تم التثبيت)")
        
        print("🔄 فحص ملفات النظام...")
        
        # Check if main files exist
        required_files = [
            "main.py",
            "database/models.py",
            "ui/login.py"
        ]
        
        for file_path in required_files:
            if os.path.exists(file_path):
                print(f"✓ {file_path}")
            else:
                print(f"❌ {file_path} مفقود")
                input("اضغط Enter للخروج...")
                return
        
        print("🚀 بدء تشغيل النظام...")
        print()
        
        # Import and run main application
        from main import MainApplication
        
        app = MainApplication()
        print("✓ تم إنشاء التطبيق")
        
        app.run()
        print("✓ تم إغلاق التطبيق")
        
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        print("\nتفاصيل الخطأ:")
        traceback.print_exc()
        print("\n" + "=" * 60)
        print("للمساعدة:")
        print("1. تأكد من تثبيت Python 3.8 أو أحدث")
        print("2. قم بتشغيل: pip install -r requirements.txt")
        print("3. تأكد من وجود جميع ملفات النظام")
        input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
