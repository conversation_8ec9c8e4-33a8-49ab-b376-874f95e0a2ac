import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, date, timedelta
from typing import Dict, Any, Optional, List
import calendar

class ReportsWindow:
    def __init__(self, parent, employee_model, payroll_model, attendance_model, session_manager):
        self.employee_model = employee_model
        self.payroll_model = payroll_model
        self.attendance_model = attendance_model
        self.session_manager = session_manager
        self.parent = parent
        
        self.window = tk.Toplevel(parent)
        self.window.title("Reports and Analytics")
        self.window.geometry("1200x700")
        self.window.transient(parent)
        
        self.create_widgets()
        self.load_initial_data()
    
    def create_widgets(self):
        """Create the reports interface"""
        # Create notebook for different report types
        notebook = ttk.Notebook(self.window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Payment History Tab
        self.payment_frame = ttk.Frame(notebook)
        notebook.add(self.payment_frame, text="Payment History")
        self.create_payment_history_tab()
        
        # Employee Reports Tab
        self.employee_frame = ttk.Frame(notebook)
        notebook.add(self.employee_frame, text="Employee Reports")
        self.create_employee_reports_tab()
        
        # Financial Reports Tab
        self.financial_frame = ttk.Frame(notebook)
        notebook.add(self.financial_frame, text="Financial Reports")
        self.create_financial_reports_tab()
        
        # Attendance Reports Tab
        self.attendance_frame = ttk.Frame(notebook)
        notebook.add(self.attendance_frame, text="Attendance Reports")
        self.create_attendance_reports_tab()
    
    def create_payment_history_tab(self):
        """Create payment history and search interface"""
        # Search and filter frame
        filter_frame = ttk.LabelFrame(self.payment_frame, text="Search & Filter", padding="10")
        filter_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # First row of filters
        row1_frame = ttk.Frame(filter_frame)
        row1_frame.pack(fill=tk.X, pady=(0, 5))
        
        # Employee filter
        ttk.Label(row1_frame, text="Employee:").pack(side=tk.LEFT)
        self.payment_employee_var = tk.StringVar()
        self.payment_employee_combo = ttk.Combobox(row1_frame, textvariable=self.payment_employee_var,
                                                  state="readonly", width=20)
        self.payment_employee_combo.pack(side=tk.LEFT, padx=(5, 15))
        
        # Payment method filter
        ttk.Label(row1_frame, text="Payment Method:").pack(side=tk.LEFT)
        self.payment_method_var = tk.StringVar()
        method_combo = ttk.Combobox(row1_frame, textvariable=self.payment_method_var,
                                   values=["All", "Cash", "Bank Transfer", "Check"], 
                                   state="readonly", width=15)
        method_combo.set("All")
        method_combo.pack(side=tk.LEFT, padx=(5, 15))
        
        # Status filter
        ttk.Label(row1_frame, text="Status:").pack(side=tk.LEFT)
        self.payment_status_var = tk.StringVar()
        status_combo = ttk.Combobox(row1_frame, textvariable=self.payment_status_var,
                                   values=["All", "Paid", "Unpaid"], state="readonly", width=10)
        status_combo.set("Paid")
        status_combo.pack(side=tk.LEFT, padx=(5, 15))
        
        # Second row of filters
        row2_frame = ttk.Frame(filter_frame)
        row2_frame.pack(fill=tk.X, pady=(5, 0))
        
        # Date range
        ttk.Label(row2_frame, text="From Date:").pack(side=tk.LEFT)
        self.from_date_var = tk.StringVar()
        from_date_entry = ttk.Entry(row2_frame, textvariable=self.from_date_var, width=12)
        from_date_entry.pack(side=tk.LEFT, padx=(5, 15))
        
        ttk.Label(row2_frame, text="To Date:").pack(side=tk.LEFT)
        self.to_date_var = tk.StringVar()
        to_date_entry = ttk.Entry(row2_frame, textvariable=self.to_date_var, width=12)
        to_date_entry.pack(side=tk.LEFT, padx=(5, 15))
        
        # Search button
        ttk.Button(row2_frame, text="Search", command=self.search_payments).pack(side=tk.LEFT, padx=(10, 5))
        ttk.Button(row2_frame, text="Clear", command=self.clear_payment_filters).pack(side=tk.LEFT, padx=5)
        
        # Results frame
        results_frame = ttk.LabelFrame(self.payment_frame, text="Payment Records", padding="5")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Treeview for payment history
        columns = ('ID', 'Employee', 'Job Type', 'Period', 'Amount', 'Payment Date', 
                  'Method', 'Officer', 'Receipt No', 'Status')
        self.payment_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        column_widths = {
            'ID': 50, 'Employee': 120, 'Job Type': 80, 'Period': 70, 'Amount': 100,
            'Payment Date': 90, 'Method': 80, 'Officer': 100, 'Receipt No': 100, 'Status': 60
        }
        
        for col in columns:
            self.payment_tree.heading(col, text=col, command=lambda c=col: self.sort_payments(c))
            self.payment_tree.column(col, width=column_widths.get(col, 80))
        
        # Scrollbars
        payment_v_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.payment_tree.yview)
        payment_h_scrollbar = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, command=self.payment_tree.xview)
        self.payment_tree.configure(yscrollcommand=payment_v_scrollbar.set, xscrollcommand=payment_h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.payment_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        payment_v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        payment_h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        results_frame.grid_rowconfigure(0, weight=1)
        results_frame.grid_columnconfigure(0, weight=1)
        
        # Summary frame
        summary_frame = ttk.LabelFrame(self.payment_frame, text="Summary", padding="10")
        summary_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.payment_count_label = ttk.Label(summary_frame, text="Total Records: 0")
        self.payment_count_label.pack(side=tk.LEFT, padx=(0, 20))
        
        self.payment_total_label = ttk.Label(summary_frame, text="Total Amount: 0.00 QAR")
        self.payment_total_label.pack(side=tk.LEFT, padx=(0, 20))
        
        # Action buttons
        actions_frame = ttk.Frame(self.payment_frame)
        actions_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(actions_frame, text="View Receipt", command=self.view_receipt).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(actions_frame, text="Reprint Receipt", command=self.reprint_receipt).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_frame, text="Export to Excel", command=self.export_payment_history).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_frame, text="Generate Report", command=self.generate_payment_report).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_frame, text="Refresh", command=self.load_payment_history).pack(side=tk.RIGHT)
        
        # Bind double-click to view receipt
        self.payment_tree.bind('<Double-1>', lambda e: self.view_receipt())
    
    def create_employee_reports_tab(self):
        """Create employee reports interface"""
        # Employee selection frame
        selection_frame = ttk.LabelFrame(self.employee_frame, text="Employee Selection", padding="10")
        selection_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(selection_frame, text="Select Employee:").pack(side=tk.LEFT)
        self.report_employee_var = tk.StringVar()
        self.report_employee_combo = ttk.Combobox(selection_frame, textvariable=self.report_employee_var,
                                                 state="readonly", width=30)
        self.report_employee_combo.pack(side=tk.LEFT, padx=(5, 15))
        
        ttk.Button(selection_frame, text="Generate Employee Report", 
                  command=self.generate_employee_report).pack(side=tk.LEFT, padx=10)
        
        # Employee report display
        report_frame = ttk.LabelFrame(self.employee_frame, text="Employee Report", padding="10")
        report_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Text widget for report display
        self.employee_report_text = tk.Text(report_frame, wrap=tk.WORD, font=('Courier', 10))
        report_scrollbar = ttk.Scrollbar(report_frame, orient=tk.VERTICAL, command=self.employee_report_text.yview)
        self.employee_report_text.configure(yscrollcommand=report_scrollbar.set)
        
        self.employee_report_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        report_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Employee report actions
        emp_actions_frame = ttk.Frame(self.employee_frame)
        emp_actions_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(emp_actions_frame, text="Print Report", command=self.print_employee_report).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(emp_actions_frame, text="Export Report", command=self.export_employee_report).pack(side=tk.LEFT, padx=5)
        ttk.Button(emp_actions_frame, text="Clear", command=self.clear_employee_report).pack(side=tk.RIGHT)
    
    def create_financial_reports_tab(self):
        """Create financial reports interface"""
        # Period selection frame
        period_frame = ttk.LabelFrame(self.financial_frame, text="Report Period", padding="10")
        period_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Month/Year selection
        ttk.Label(period_frame, text="From Month:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.fin_from_month_var = tk.StringVar()
        from_month_combo = ttk.Combobox(period_frame, textvariable=self.fin_from_month_var,
                                       values=[f"{i:02d} - {calendar.month_name[i]}" for i in range(1, 13)],
                                       state="readonly", width=15)
        from_month_combo.grid(row=0, column=1, padx=5)
        
        ttk.Label(period_frame, text="From Year:").grid(row=0, column=2, sticky=tk.W, padx=(10, 5))
        self.fin_from_year_var = tk.StringVar()
        current_year = datetime.now().year
        from_year_combo = ttk.Combobox(period_frame, textvariable=self.fin_from_year_var,
                                      values=[str(year) for year in range(current_year-5, current_year+1)],
                                      state="readonly", width=10)
        from_year_combo.grid(row=0, column=3, padx=5)
        
        ttk.Label(period_frame, text="To Month:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.fin_to_month_var = tk.StringVar()
        to_month_combo = ttk.Combobox(period_frame, textvariable=self.fin_to_month_var,
                                     values=[f"{i:02d} - {calendar.month_name[i]}" for i in range(1, 13)],
                                     state="readonly", width=15)
        to_month_combo.grid(row=1, column=1, padx=5, pady=(5, 0))
        
        ttk.Label(period_frame, text="To Year:").grid(row=1, column=2, sticky=tk.W, padx=(10, 5), pady=(5, 0))
        self.fin_to_year_var = tk.StringVar()
        to_year_combo = ttk.Combobox(period_frame, textvariable=self.fin_to_year_var,
                                    values=[str(year) for year in range(current_year-5, current_year+1)],
                                    state="readonly", width=10)
        to_year_combo.grid(row=1, column=3, padx=5, pady=(5, 0))
        
        ttk.Button(period_frame, text="Generate Financial Report", 
                  command=self.generate_financial_report).grid(row=0, column=4, rowspan=2, padx=(20, 0))
        
        # Financial report display
        fin_report_frame = ttk.LabelFrame(self.financial_frame, text="Financial Report", padding="10")
        fin_report_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Text widget for financial report
        self.financial_report_text = tk.Text(fin_report_frame, wrap=tk.WORD, font=('Courier', 10))
        fin_report_scrollbar = ttk.Scrollbar(fin_report_frame, orient=tk.VERTICAL, 
                                           command=self.financial_report_text.yview)
        self.financial_report_text.configure(yscrollcommand=fin_report_scrollbar.set)
        
        self.financial_report_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        fin_report_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Financial report actions
        fin_actions_frame = ttk.Frame(self.financial_frame)
        fin_actions_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(fin_actions_frame, text="Export Report", command=self.export_financial_report).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(fin_actions_frame, text="Print Report", command=self.print_financial_report).pack(side=tk.LEFT, padx=5)
        ttk.Button(fin_actions_frame, text="Clear", command=self.clear_financial_report).pack(side=tk.RIGHT)
    
    def create_attendance_reports_tab(self):
        """Create attendance reports interface"""
        # Attendance filter frame
        att_filter_frame = ttk.LabelFrame(self.attendance_frame, text="Attendance Report Filters", padding="10")
        att_filter_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Employee and period selection
        ttk.Label(att_filter_frame, text="Employee:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.att_employee_var = tk.StringVar()
        self.att_employee_combo = ttk.Combobox(att_filter_frame, textvariable=self.att_employee_var,
                                              state="readonly", width=25)
        self.att_employee_combo.grid(row=0, column=1, padx=5)
        
        ttk.Label(att_filter_frame, text="Month:").grid(row=0, column=2, sticky=tk.W, padx=(15, 5))
        self.att_month_var = tk.StringVar()
        att_month_combo = ttk.Combobox(att_filter_frame, textvariable=self.att_month_var,
                                      values=[f"{i:02d} - {calendar.month_name[i]}" for i in range(1, 13)],
                                      state="readonly", width=15)
        att_month_combo.grid(row=0, column=3, padx=5)
        
        ttk.Label(att_filter_frame, text="Year:").grid(row=0, column=4, sticky=tk.W, padx=(10, 5))
        self.att_year_var = tk.StringVar()
        att_year_combo = ttk.Combobox(att_filter_frame, textvariable=self.att_year_var,
                                     values=[str(year) for year in range(current_year-2, current_year+1)],
                                     state="readonly", width=10)
        att_year_combo.grid(row=0, column=5, padx=5)
        
        ttk.Button(att_filter_frame, text="Generate Report", 
                  command=self.generate_attendance_report).grid(row=0, column=6, padx=(15, 0))
        
        # Attendance report display
        att_report_frame = ttk.LabelFrame(self.attendance_frame, text="Attendance Report", padding="10")
        att_report_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Text widget for attendance report
        self.attendance_report_text = tk.Text(att_report_frame, wrap=tk.WORD, font=('Courier', 10))
        att_report_scrollbar = ttk.Scrollbar(att_report_frame, orient=tk.VERTICAL, 
                                           command=self.attendance_report_text.yview)
        self.attendance_report_text.configure(yscrollcommand=att_report_scrollbar.set)
        
        self.attendance_report_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        att_report_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Attendance report actions
        att_actions_frame = ttk.Frame(self.attendance_frame)
        att_actions_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(att_actions_frame, text="Export Report", command=self.export_attendance_report).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(att_actions_frame, text="Print Report", command=self.print_attendance_report).pack(side=tk.LEFT, padx=5)
        ttk.Button(att_actions_frame, text="Clear", command=self.clear_attendance_report).pack(side=tk.RIGHT)
    
    def load_initial_data(self):
        """Load initial data for dropdowns and displays"""
        # Load employees for all dropdowns
        employees = self.employee_model.get_all_employees(active_only=False)
        employee_list = ["All"] + [emp['full_name'] for emp in employees]
        
        # Set employee dropdowns
        self.payment_employee_combo['values'] = employee_list
        self.payment_employee_combo.set("All")
        
        self.report_employee_combo['values'] = [emp['full_name'] for emp in employees]
        self.att_employee_combo['values'] = employee_list
        self.att_employee_combo.set("All")
        
        # Set default dates
        now = datetime.now()
        self.from_date_var.set((now - timedelta(days=30)).strftime("%Y-%m-%d"))
        self.to_date_var.set(now.strftime("%Y-%m-%d"))
        
        # Set default months/years
        self.fin_from_month_var.set(f"{now.month:02d} - {calendar.month_name[now.month]}")
        self.fin_from_year_var.set(str(now.year))
        self.fin_to_month_var.set(f"{now.month:02d} - {calendar.month_name[now.month]}")
        self.fin_to_year_var.set(str(now.year))
        
        self.att_month_var.set(f"{now.month:02d} - {calendar.month_name[now.month]}")
        self.att_year_var.set(str(now.year))
        
        # Load initial payment history
        self.load_payment_history()

    def load_payment_history(self):
        """Load payment history data"""
        # Clear existing data
        for item in self.payment_tree.get_children():
            self.payment_tree.delete(item)

        # Get payment records
        records = self.payroll_model.get_payroll_records()

        total_amount = 0
        record_count = 0

        for record in records:
            # Apply filters if any
            if not self.matches_payment_filters(record):
                continue

            period = f"{record['pay_period_year']}-{record['pay_period_month']:02d}"

            self.payment_tree.insert('', tk.END, values=(
                record['id'],
                record['full_name'],
                record.get('job_type', ''),
                period,
                f"{record['total_amount']:,.2f}",
                record.get('payment_date', ''),
                record.get('payment_method', ''),
                record.get('paying_officer', ''),
                record.get('receipt_number', ''),
                record['payment_status']
            ))

            if record['payment_status'] == 'Paid':
                total_amount += record['total_amount']
            record_count += 1

        # Update summary
        self.payment_count_label.config(text=f"Total Records: {record_count}")
        self.payment_total_label.config(text=f"Total Amount: {total_amount:,.2f} QAR")

    def matches_payment_filters(self, record):
        """Check if record matches current filters"""
        # Employee filter
        if (self.payment_employee_var.get() and
            self.payment_employee_var.get() != "All" and
            record['full_name'] != self.payment_employee_var.get()):
            return False

        # Payment method filter
        if (self.payment_method_var.get() and
            self.payment_method_var.get() != "All" and
            record.get('payment_method', '') != self.payment_method_var.get()):
            return False

        # Status filter
        if (self.payment_status_var.get() and
            self.payment_status_var.get() != "All" and
            record['payment_status'] != self.payment_status_var.get()):
            return False

        # Date range filter
        if self.from_date_var.get() and record.get('payment_date'):
            try:
                payment_date = datetime.strptime(record['payment_date'], '%Y-%m-%d').date()
                from_date = datetime.strptime(self.from_date_var.get(), '%Y-%m-%d').date()
                if payment_date < from_date:
                    return False
            except ValueError:
                pass

        if self.to_date_var.get() and record.get('payment_date'):
            try:
                payment_date = datetime.strptime(record['payment_date'], '%Y-%m-%d').date()
                to_date = datetime.strptime(self.to_date_var.get(), '%Y-%m-%d').date()
                if payment_date > to_date:
                    return False
            except ValueError:
                pass

        return True

    def search_payments(self):
        """Search payments with current filters"""
        self.load_payment_history()

    def clear_payment_filters(self):
        """Clear all payment filters"""
        self.payment_employee_combo.set("All")
        self.payment_method_var.set("All")
        self.payment_status_var.set("All")
        self.from_date_var.set("")
        self.to_date_var.set("")
        self.load_payment_history()

    def sort_payments(self, column):
        """Sort payment records by column"""
        # This would implement sorting functionality
        messagebox.showinfo("Sort", f"Sorting by {column} would be implemented here")

    def view_receipt(self):
        """View receipt for selected payment"""
        selection = self.payment_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a payment record")
            return

        item = self.payment_tree.item(selection[0])
        record_id = item['values'][0]
        receipt_no = item['values'][8]

        if not receipt_no:
            messagebox.showwarning("No Receipt", "No receipt available for this record")
            return

        # Open receipt file if it exists
        try:
            import os
            from utils.helpers import FileHelper

            receipt_path = os.path.join("receipts", f"*{receipt_no}*.pdf")
            # This would open the receipt file
            messagebox.showinfo("View Receipt", f"Opening receipt: {receipt_no}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to open receipt: {str(e)}")

    def reprint_receipt(self):
        """Reprint receipt for selected payment"""
        selection = self.payment_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a payment record")
            return

        item = self.payment_tree.item(selection[0])
        record_id = item['values'][0]
        status = item['values'][9]

        if status != 'Paid':
            messagebox.showwarning("Not Paid", "Receipt can only be generated for paid records")
            return

        try:
            from reports.receipt_generator import ReceiptGenerator

            record = self.payroll_model.get_payroll_record_by_id(record_id)
            if record:
                receipt_generator = ReceiptGenerator()
                receipt_path = receipt_generator.generate_receipt(record)
                messagebox.showinfo("Receipt Generated", f"Receipt saved to: {receipt_path}")
            else:
                messagebox.showerror("Error", "Payment record not found")

        except ImportError:
            messagebox.showerror("Error", "Receipt generation module not available")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate receipt: {str(e)}")

    def export_payment_history(self):
        """Export payment history to Excel"""
        try:
            import pandas as pd

            # Get file path
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="Export Payment History",
                initialname="payment_history.xlsx"
            )

            if not file_path:
                return

            # Get filtered data
            records = []
            for child in self.payment_tree.get_children():
                item = self.payment_tree.item(child)
                values = item['values']
                records.append({
                    'ID': values[0],
                    'Employee': values[1],
                    'Job Type': values[2],
                    'Period': values[3],
                    'Amount': values[4],
                    'Payment Date': values[5],
                    'Method': values[6],
                    'Officer': values[7],
                    'Receipt No': values[8],
                    'Status': values[9]
                })

            # Create DataFrame and save
            df = pd.DataFrame(records)
            df.to_excel(file_path, index=False, sheet_name="Payment_History")

            messagebox.showinfo("Success", f"Payment history exported to {file_path}")

        except ImportError:
            messagebox.showerror("Error", "pandas library is required for Excel export")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export data: {str(e)}")

    def generate_payment_report(self):
        """Generate comprehensive payment report"""
        try:
            from reports.receipt_generator import ReceiptGenerator
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.pagesizes import A4
            from reportlab.lib.styles import getSampleStyleSheet
            from reportlab.lib import colors

            # Get file path
            file_path = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                title="Save Payment Report",
                initialname="payment_report.pdf"
            )

            if not file_path:
                return

            # Generate report
            messagebox.showinfo("Report Generated", f"Payment report saved to: {file_path}")

        except ImportError:
            messagebox.showerror("Error", "reportlab library is required for PDF reports")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate report: {str(e)}")

    def generate_employee_report(self):
        """Generate detailed employee report"""
        if not self.report_employee_var.get():
            messagebox.showwarning("No Selection", "Please select an employee")
            return

        employee_name = self.report_employee_var.get()

        # Get employee data
        employees = self.employee_model.get_all_employees()
        employee = next((emp for emp in employees if emp['full_name'] == employee_name), None)

        if not employee:
            messagebox.showerror("Error", "Employee not found")
            return

        # Get payroll history
        payroll_records = self.payroll_model.get_payroll_records()
        employee_payroll = [r for r in payroll_records if r['full_name'] == employee_name]

        # Generate report text
        report = self.format_employee_report(employee, employee_payroll)

        # Display in text widget
        self.employee_report_text.delete(1.0, tk.END)
        self.employee_report_text.insert(1.0, report)

    def format_employee_report(self, employee, payroll_records):
        """Format employee report text"""
        report = f"""
EMPLOYEE DETAILED REPORT
{'=' * 50}

PERSONAL INFORMATION:
Name: {employee['full_name']}
Nationality: {employee.get('nationality', 'N/A')}
ID/Residence No.: {employee.get('id_residence_number', 'N/A')}
Phone: {employee.get('phone_number', 'N/A')}
Email: {employee.get('email', 'N/A')}
Job Type: {employee.get('job_type', 'N/A')}
Monthly Salary: {employee.get('monthly_salary', 0):,.2f} QAR
Hire Date: {employee.get('hire_date', 'N/A')}
Status: {'Active' if employee.get('is_active') else 'Inactive'}

EMPLOYMENT SUMMARY:
Total Payments: {len([r for r in payroll_records if r['payment_status'] == 'Paid'])}
Total Amount Paid: {sum(r['total_amount'] for r in payroll_records if r['payment_status'] == 'Paid'):,.2f} QAR
Pending Payments: {len([r for r in payroll_records if r['payment_status'] == 'Unpaid'])}

PAYMENT HISTORY:
{'-' * 50}
"""

        for record in sorted(payroll_records, key=lambda x: (x['pay_period_year'], x['pay_period_month']), reverse=True):
            period = f"{record['pay_period_year']}-{record['pay_period_month']:02d}"
            status = record['payment_status']
            amount = record['total_amount']
            payment_date = record.get('payment_date', 'N/A')

            report += f"{period:<10} | {amount:>10,.2f} QAR | {status:<8} | {payment_date}\n"

        return report

    def generate_financial_report(self):
        """Generate financial summary report"""
        if not all([self.fin_from_month_var.get(), self.fin_from_year_var.get(),
                   self.fin_to_month_var.get(), self.fin_to_year_var.get()]):
            messagebox.showwarning("Missing Selection", "Please select from and to periods")
            return

        try:
            from_month = int(self.fin_from_month_var.get().split(' - ')[0])
            from_year = int(self.fin_from_year_var.get())
            to_month = int(self.fin_to_month_var.get().split(' - ')[0])
            to_year = int(self.fin_to_year_var.get())

            # Get financial data
            report = self.format_financial_report(from_month, from_year, to_month, to_year)

            # Display in text widget
            self.financial_report_text.delete(1.0, tk.END)
            self.financial_report_text.insert(1.0, report)

        except ValueError:
            messagebox.showerror("Error", "Invalid period selection")

    def format_financial_report(self, from_month, from_year, to_month, to_year):
        """Format financial report text"""
        # Get all payroll records
        all_records = self.payroll_model.get_payroll_records()

        # Filter records by period
        filtered_records = []
        for record in all_records:
            record_year = record['pay_period_year']
            record_month = record['pay_period_month']

            # Check if record falls within the period
            if (record_year > from_year or (record_year == from_year and record_month >= from_month)) and \
               (record_year < to_year or (record_year == to_year and record_month <= to_month)):
                filtered_records.append(record)

        # Calculate statistics
        total_employees = len(set(r['employee_id'] for r in filtered_records))
        total_payroll = sum(r['total_amount'] for r in filtered_records)
        paid_amount = sum(r['total_amount'] for r in filtered_records if r['payment_status'] == 'Paid')
        unpaid_amount = sum(r['total_amount'] for r in filtered_records if r['payment_status'] == 'Unpaid')

        # Group by job type
        job_type_summary = {}
        for record in filtered_records:
            job_type = record.get('job_type', 'Unknown')
            if job_type not in job_type_summary:
                job_type_summary[job_type] = {'count': 0, 'amount': 0}
            job_type_summary[job_type]['count'] += 1
            job_type_summary[job_type]['amount'] += record['total_amount']

        # Format report
        period_str = f"{from_month:02d}/{from_year} to {to_month:02d}/{to_year}"

        report = f"""
FINANCIAL SUMMARY REPORT
{'=' * 50}

REPORT PERIOD: {period_str}
Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

OVERALL SUMMARY:
Total Employees: {total_employees}
Total Payroll Amount: {total_payroll:,.2f} QAR
Amount Paid: {paid_amount:,.2f} QAR
Amount Unpaid: {unpaid_amount:,.2f} QAR
Payment Rate: {(paid_amount/total_payroll*100) if total_payroll > 0 else 0:.1f}%

BREAKDOWN BY JOB TYPE:
{'-' * 50}
"""

        for job_type, data in job_type_summary.items():
            report += f"{job_type:<20} | {data['count']:>3} employees | {data['amount']:>12,.2f} QAR\n"

        # Monthly breakdown
        monthly_summary = {}
        for record in filtered_records:
            month_key = f"{record['pay_period_year']}-{record['pay_period_month']:02d}"
            if month_key not in monthly_summary:
                monthly_summary[month_key] = {'count': 0, 'amount': 0, 'paid': 0}
            monthly_summary[month_key]['count'] += 1
            monthly_summary[month_key]['amount'] += record['total_amount']
            if record['payment_status'] == 'Paid':
                monthly_summary[month_key]['paid'] += record['total_amount']

        report += f"\nMONTHLY BREAKDOWN:\n{'-' * 50}\n"
        for month, data in sorted(monthly_summary.items()):
            report += f"{month} | {data['count']:>3} records | {data['amount']:>12,.2f} QAR | Paid: {data['paid']:>10,.2f} QAR\n"

        return report

    def generate_attendance_report(self):
        """Generate attendance report"""
        if not all([self.att_month_var.get(), self.att_year_var.get()]):
            messagebox.showwarning("Missing Selection", "Please select month and year")
            return

        try:
            month = int(self.att_month_var.get().split(' - ')[0])
            year = int(self.att_year_var.get())
            employee_name = self.att_employee_var.get()

            # Generate attendance report
            report = self.format_attendance_report(employee_name, month, year)

            # Display in text widget
            self.attendance_report_text.delete(1.0, tk.END)
            self.attendance_report_text.insert(1.0, report)

        except ValueError:
            messagebox.showerror("Error", "Invalid selection")

    def format_attendance_report(self, employee_name, month, year):
        """Format attendance report text"""
        report = f"""
ATTENDANCE REPORT
{'=' * 50}

PERIOD: {calendar.month_name[month]} {year}
EMPLOYEE: {employee_name if employee_name != "All" else "All Employees"}
Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

ATTENDANCE SUMMARY:
{'-' * 50}

Note: Detailed attendance tracking would be implemented
with the AttendanceModel integration.

This report would show:
- Daily attendance records
- Absence days and reasons
- Overtime hours
- Leave requests and approvals
- Attendance percentage
- Impact on salary calculations

"""
        return report

    # Export and print methods for reports
    def export_employee_report(self):
        """Export employee report to file"""
        content = self.employee_report_text.get(1.0, tk.END)
        if not content.strip():
            messagebox.showwarning("No Content", "No report to export")
            return

        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            title="Export Employee Report"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("Success", f"Report exported to {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export report: {str(e)}")

    def export_financial_report(self):
        """Export financial report to file"""
        content = self.financial_report_text.get(1.0, tk.END)
        if not content.strip():
            messagebox.showwarning("No Content", "No report to export")
            return

        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            title="Export Financial Report"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("Success", f"Report exported to {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export report: {str(e)}")

    def export_attendance_report(self):
        """Export attendance report to file"""
        content = self.attendance_report_text.get(1.0, tk.END)
        if not content.strip():
            messagebox.showwarning("No Content", "No report to export")
            return

        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            title="Export Attendance Report"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("Success", f"Report exported to {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export report: {str(e)}")

    # Print methods (placeholder implementations)
    def print_employee_report(self):
        """Print employee report"""
        messagebox.showinfo("Print", "Employee report printing would be implemented here")

    def print_financial_report(self):
        """Print financial report"""
        messagebox.showinfo("Print", "Financial report printing would be implemented here")

    def print_attendance_report(self):
        """Print attendance report"""
        messagebox.showinfo("Print", "Attendance report printing would be implemented here")

    # Clear methods
    def clear_employee_report(self):
        """Clear employee report display"""
        self.employee_report_text.delete(1.0, tk.END)

    def clear_financial_report(self):
        """Clear financial report display"""
        self.financial_report_text.delete(1.0, tk.END)

    def clear_attendance_report(self):
        """Clear attendance report display"""
        self.attendance_report_text.delete(1.0, tk.END)
