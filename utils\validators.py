import re
from datetime import datetime
from typing import Dict, List, Any

class ValidationError(Exception):
    """Custom validation error"""
    pass

class Validators:
    @staticmethod
    def validate_email(email: str) -> bool:
        """Validate email format"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def validate_phone(phone: str) -> bool:
        """Validate phone number format"""
        pattern = r'^\+?[\d\s\-\(\)]{8,15}$'
        return re.match(pattern, phone) is not None
    
    @staticmethod
    def validate_id_number(id_number: str) -> bool:
        """Validate ID/residence number format"""
        # Allow alphanumeric characters and basic punctuation
        pattern = r'^[A-Za-z0-9\-\/]{5,20}$'
        return re.match(pattern, id_number) is not None
    
    @staticmethod
    def validate_salary(salary: str) -> bool:
        """Validate salary amount"""
        try:
            amount = float(salary)
            return amount >= 0
        except ValueError:
            return False
    
    @staticmethod
    def validate_date(date_str: str) -> bool:
        """Validate date format (YYYY-MM-DD)"""
        try:
            datetime.strptime(date_str, '%Y-%m-%d')
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> List[str]:
        """Validate that all required fields are present and not empty"""
        errors = []
        for field in required_fields:
            if field not in data or not str(data[field]).strip():
                errors.append(f"{field.replace('_', ' ').title()} is required")
        return errors
    
    @staticmethod
    def validate_employee_data(data: Dict[str, Any]) -> List[str]:
        """Validate employee data"""
        errors = []
        
        # Required fields
        required_fields = ['full_name', 'nationality', 'id_residence_number', 
                          'job_type', 'monthly_salary', 'start_date']
        errors.extend(Validators.validate_required_fields(data, required_fields))
        
        # Specific validations
        if 'id_residence_number' in data and data['id_residence_number']:
            if not Validators.validate_id_number(data['id_residence_number']):
                errors.append("Invalid ID/Residence number format")
        
        if 'monthly_salary' in data and data['monthly_salary']:
            if not Validators.validate_salary(str(data['monthly_salary'])):
                errors.append("Invalid salary amount")
        
        if 'start_date' in data and data['start_date']:
            if not Validators.validate_date(data['start_date']):
                errors.append("Invalid start date format (use YYYY-MM-DD)")
        
        return errors
    
    @staticmethod
    def validate_user_data(data: Dict[str, Any]) -> List[str]:
        """Validate user data"""
        errors = []
        
        # Required fields
        required_fields = ['username', 'password', 'role', 'full_name']
        errors.extend(Validators.validate_required_fields(data, required_fields))
        
        # Username validation
        if 'username' in data and data['username']:
            if len(data['username']) < 3:
                errors.append("Username must be at least 3 characters long")
            if not re.match(r'^[a-zA-Z0-9_]+$', data['username']):
                errors.append("Username can only contain letters, numbers, and underscores")
        
        # Password validation
        if 'password' in data and data['password']:
            if len(data['password']) < 6:
                errors.append("Password must be at least 6 characters long")
        
        # Role validation
        if 'role' in data and data['role']:
            valid_roles = ['Admin', 'Supervisor', 'Regular User']
            if data['role'] not in valid_roles:
                errors.append(f"Role must be one of: {', '.join(valid_roles)}")
        
        return errors
    
    @staticmethod
    def validate_payroll_data(data: Dict[str, Any]) -> List[str]:
        """Validate payroll data"""
        errors = []
        
        # Required fields
        required_fields = ['employee_id', 'pay_period_month', 'pay_period_year', 
                          'base_salary', 'total_amount']
        errors.extend(Validators.validate_required_fields(data, required_fields))
        
        # Month validation
        if 'pay_period_month' in data:
            try:
                month = int(data['pay_period_month'])
                if not 1 <= month <= 12:
                    errors.append("Month must be between 1 and 12")
            except (ValueError, TypeError):
                errors.append("Invalid month format")
        
        # Year validation
        if 'pay_period_year' in data:
            try:
                year = int(data['pay_period_year'])
                current_year = datetime.now().year
                if not 2000 <= year <= current_year + 1:
                    errors.append(f"Year must be between 2000 and {current_year + 1}")
            except (ValueError, TypeError):
                errors.append("Invalid year format")
        
        # Salary validation
        for field in ['base_salary', 'total_amount', 'overtime_amount', 'absence_deduction']:
            if field in data and data[field] is not None:
                if not Validators.validate_salary(str(data[field])):
                    errors.append(f"Invalid {field.replace('_', ' ')} amount")
        
        # Hours validation
        if 'overtime_hours' in data and data['overtime_hours'] is not None:
            try:
                hours = float(data['overtime_hours'])
                if hours < 0:
                    errors.append("Overtime hours cannot be negative")
            except (ValueError, TypeError):
                errors.append("Invalid overtime hours format")
        
        # Days validation
        if 'absence_days' in data and data['absence_days'] is not None:
            try:
                days = int(data['absence_days'])
                if days < 0 or days > 31:
                    errors.append("Absence days must be between 0 and 31")
            except (ValueError, TypeError):
                errors.append("Invalid absence days format")
        
        return errors
