import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
from typing import Dict, Any, Optional, List
from utils.validators import Validators
from utils.helpers import PayrollCalculator, DateHelper, ReceiptHelper
import calendar

class PayrollProcessingWindow:
    def __init__(self, parent, employee_model, payroll_model, attendance_model, session_manager):
        self.employee_model = employee_model
        self.payroll_model = payroll_model
        self.attendance_model = attendance_model
        self.session_manager = session_manager
        self.parent = parent
        
        # Check permissions
        if not session_manager.has_permission('Supervisor'):
            messagebox.showerror("Access Denied", "Insufficient permissions to process payroll")
            return
        
        self.window = tk.Toplevel(parent)
        self.window.title("Payroll Processing")
        self.window.geometry("1200x700")
        self.window.transient(parent)
        
        self.create_widgets()
        self.load_current_month()
    
    def create_widgets(self):
        """Create the payroll processing interface"""
        # Create notebook for tabs
        notebook = ttk.Notebook(self.window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Monthly Payroll Tab
        self.monthly_frame = ttk.Frame(notebook)
        notebook.add(self.monthly_frame, text="Monthly Payroll")
        self.create_monthly_tab()
        
        # Payment History Tab
        self.history_frame = ttk.Frame(notebook)
        notebook.add(self.history_frame, text="Payment History")
        self.create_history_tab()
    
    def create_monthly_tab(self):
        """Create monthly payroll processing tab"""
        # Period selection frame
        period_frame = ttk.LabelFrame(self.monthly_frame, text="Pay Period", padding="10")
        period_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(period_frame, text="Month:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.month_var = tk.StringVar()
        month_combo = ttk.Combobox(period_frame, textvariable=self.month_var, 
                                  values=[f"{i:02d} - {calendar.month_name[i]}" for i in range(1, 13)],
                                  state="readonly", width=15)
        month_combo.grid(row=0, column=1, padx=5)
        
        ttk.Label(period_frame, text="Year:").grid(row=0, column=2, sticky=tk.W, padx=(20, 5))
        self.year_var = tk.StringVar()
        current_year = datetime.now().year
        year_combo = ttk.Combobox(period_frame, textvariable=self.year_var,
                                 values=[str(year) for year in range(current_year-2, current_year+2)],
                                 state="readonly", width=10)
        year_combo.grid(row=0, column=3, padx=5)
        
        ttk.Button(period_frame, text="Load Payroll", command=self.load_payroll_data).grid(
            row=0, column=4, padx=(20, 0))
        
        ttk.Button(period_frame, text="Generate Payroll", command=self.generate_payroll).grid(
            row=0, column=5, padx=10)
        
        # Payroll data frame
        data_frame = ttk.LabelFrame(self.monthly_frame, text="Employee Payroll", padding="5")
        data_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Treeview for payroll data
        columns = ('ID', 'Employee', 'Job Type', 'Base Salary', 'Overtime Hrs', 'Overtime Amt', 
                  'Absence Days', 'Deduction', 'Total Amount', 'Status', 'Actions')
        self.payroll_tree = ttk.Treeview(data_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        column_widths = {
            'ID': 50, 'Employee': 150, 'Job Type': 100, 'Base Salary': 100,
            'Overtime Hrs': 80, 'Overtime Amt': 100, 'Absence Days': 80,
            'Deduction': 100, 'Total Amount': 120, 'Status': 80, 'Actions': 100
        }
        
        for col in columns:
            self.payroll_tree.heading(col, text=col)
            self.payroll_tree.column(col, width=column_widths.get(col, 100))
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(data_frame, orient=tk.VERTICAL, command=self.payroll_tree.yview)
        h_scrollbar = ttk.Scrollbar(data_frame, orient=tk.HORIZONTAL, command=self.payroll_tree.xview)
        self.payroll_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.payroll_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        data_frame.grid_rowconfigure(0, weight=1)
        data_frame.grid_columnconfigure(0, weight=1)
        
        # Summary frame
        summary_frame = ttk.LabelFrame(self.monthly_frame, text="Payroll Summary", padding="10")
        summary_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.total_employees_label = ttk.Label(summary_frame, text="Total Employees: 0")
        self.total_employees_label.grid(row=0, column=0, sticky=tk.W, padx=(0, 20))
        
        self.total_amount_label = ttk.Label(summary_frame, text="Total Amount: 0.00 QAR")
        self.total_amount_label.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        
        self.paid_count_label = ttk.Label(summary_frame, text="Paid: 0")
        self.paid_count_label.grid(row=0, column=2, sticky=tk.W, padx=(0, 20))
        
        self.unpaid_count_label = ttk.Label(summary_frame, text="Unpaid: 0")
        self.unpaid_count_label.grid(row=0, column=3, sticky=tk.W)
        
        # Action buttons frame
        actions_frame = ttk.Frame(self.monthly_frame)
        actions_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(actions_frame, text="Edit Payroll", command=self.edit_payroll_record).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(actions_frame, text="Pay Salary", command=self.pay_salary).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_frame, text="Generate Receipt", command=self.generate_receipt).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_frame, text="Export to Excel", command=self.export_payroll).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_frame, text="Refresh", command=self.load_payroll_data).pack(side=tk.RIGHT)
        
        # Bind double-click to edit
        self.payroll_tree.bind('<Double-1>', lambda e: self.edit_payroll_record())
    
    def create_history_tab(self):
        """Create payment history tab"""
        # Filter frame
        filter_frame = ttk.LabelFrame(self.history_frame, text="Filters", padding="10")
        filter_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Employee filter
        ttk.Label(filter_frame, text="Employee:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.history_employee_var = tk.StringVar()
        self.history_employee_combo = ttk.Combobox(filter_frame, textvariable=self.history_employee_var,
                                                  state="readonly", width=20)
        self.history_employee_combo.grid(row=0, column=1, padx=5)
        
        # Date range
        ttk.Label(filter_frame, text="From Date:").grid(row=0, column=2, sticky=tk.W, padx=(20, 5))
        self.from_date_var = tk.StringVar()
        from_date_entry = ttk.Entry(filter_frame, textvariable=self.from_date_var, width=12)
        from_date_entry.grid(row=0, column=3, padx=5)
        
        ttk.Label(filter_frame, text="To Date:").grid(row=0, column=4, sticky=tk.W, padx=(10, 5))
        self.to_date_var = tk.StringVar()
        to_date_entry = ttk.Entry(filter_frame, textvariable=self.to_date_var, width=12)
        to_date_entry.grid(row=0, column=5, padx=5)
        
        ttk.Button(filter_frame, text="Filter", command=self.filter_history).grid(
            row=0, column=6, padx=(20, 0))
        
        # History data frame
        history_data_frame = ttk.LabelFrame(self.history_frame, text="Payment History", padding="5")
        history_data_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Treeview for history
        history_columns = ('ID', 'Employee', 'Period', 'Amount', 'Payment Date', 
                          'Payment Method', 'Officer', 'Receipt No')
        self.history_tree = ttk.Treeview(history_data_frame, columns=history_columns, 
                                        show='headings', height=15)
        
        # Configure history columns
        history_widths = {
            'ID': 50, 'Employee': 150, 'Period': 100, 'Amount': 120,
            'Payment Date': 100, 'Payment Method': 100, 'Officer': 120, 'Receipt No': 120
        }
        
        for col in history_columns:
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=history_widths.get(col, 100))
        
        # History scrollbars
        history_v_scrollbar = ttk.Scrollbar(history_data_frame, orient=tk.VERTICAL, 
                                           command=self.history_tree.yview)
        history_h_scrollbar = ttk.Scrollbar(history_data_frame, orient=tk.HORIZONTAL, 
                                           command=self.history_tree.xview)
        self.history_tree.configure(yscrollcommand=history_v_scrollbar.set, 
                                   xscrollcommand=history_h_scrollbar.set)
        
        # Pack history treeview and scrollbars
        self.history_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        history_v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        history_h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        history_data_frame.grid_rowconfigure(0, weight=1)
        history_data_frame.grid_columnconfigure(0, weight=1)
        
        # History action buttons
        history_actions_frame = ttk.Frame(self.history_frame)
        history_actions_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(history_actions_frame, text="View Receipt", command=self.view_receipt).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(history_actions_frame, text="Reprint Receipt", command=self.reprint_receipt).pack(side=tk.LEFT, padx=5)
        ttk.Button(history_actions_frame, text="Export History", command=self.export_history).pack(side=tk.LEFT, padx=5)
        ttk.Button(history_actions_frame, text="Refresh", command=self.load_payment_history).pack(side=tk.RIGHT)
        
        # Load employee list for filter
        self.load_employee_filter()
        self.load_payment_history()
    
    def load_current_month(self):
        """Load current month and year"""
        now = datetime.now()
        self.month_var.set(f"{now.month:02d} - {calendar.month_name[now.month]}")
        self.year_var.set(str(now.year))
    
    def load_payroll_data(self):
        """Load payroll data for selected period"""
        if not self.month_var.get() or not self.year_var.get():
            messagebox.showwarning("Missing Selection", "Please select month and year")
            return
        
        month = int(self.month_var.get().split(' - ')[0])
        year = int(self.year_var.get())
        
        # Clear existing data
        for item in self.payroll_tree.get_children():
            self.payroll_tree.delete(item)
        
        # Load payroll records
        records = self.payroll_model.get_payroll_records(month=month, year=year)
        
        total_amount = 0
        paid_count = 0
        unpaid_count = 0
        
        for record in records:
            status = record['payment_status']
            if status == 'Paid':
                paid_count += 1
            else:
                unpaid_count += 1
            
            total_amount += record['total_amount']
            
            self.payroll_tree.insert('', tk.END, values=(
                record['id'],
                record['full_name'],
                record['job_type'],
                f"{record['base_salary']:,.2f}",
                record.get('overtime_hours', 0),
                f"{record.get('overtime_amount', 0):,.2f}",
                record.get('absence_days', 0),
                f"{record.get('absence_deduction', 0):,.2f}",
                f"{record['total_amount']:,.2f}",
                status,
                "Pay" if status == 'Unpaid' else "Paid"
            ))
        
        # Update summary
        self.update_summary(len(records), total_amount, paid_count, unpaid_count)
    
    def update_summary(self, total_employees, total_amount, paid_count, unpaid_count):
        """Update payroll summary labels"""
        self.total_employees_label.config(text=f"Total Employees: {total_employees}")
        self.total_amount_label.config(text=f"Total Amount: {total_amount:,.2f} QAR")
        self.paid_count_label.config(text=f"Paid: {paid_count}")
        self.unpaid_count_label.config(text=f"Unpaid: {unpaid_count}")
    
    def generate_payroll(self):
        """Generate payroll for all active employees for the selected period"""
        if not self.month_var.get() or not self.year_var.get():
            messagebox.showwarning("Missing Selection", "Please select month and year")
            return
        
        month = int(self.month_var.get().split(' - ')[0])
        year = int(self.year_var.get())
        
        # Check if payroll already exists
        existing_records = self.payroll_model.get_payroll_records(month=month, year=year)
        if existing_records:
            if not messagebox.askyesno("Payroll Exists", 
                                     "Payroll records already exist for this period. "
                                     "Do you want to regenerate them?"):
                return
        
        # Get all active employees
        employees = self.employee_model.get_all_employees(active_only=True)
        
        if not employees:
            messagebox.showwarning("No Employees", "No active employees found")
            return
        
        generated_count = 0
        errors = []
        
        for employee in employees:
            try:
                # Calculate absence days
                absence_days = self.attendance_model.get_absence_days(employee['id'], month, year)
                
                # Calculate payroll
                base_salary = employee['monthly_salary']
                absence_deduction = PayrollCalculator.calculate_absence_deduction(
                    base_salary, absence_days)
                
                total_amount = base_salary - absence_deduction
                
                payroll_data = {
                    'employee_id': employee['id'],
                    'pay_period_month': month,
                    'pay_period_year': year,
                    'base_salary': base_salary,
                    'absence_days': absence_days,
                    'absence_deduction': absence_deduction,
                    'total_amount': total_amount
                }
                
                # Create or update payroll record
                success = self.payroll_model.create_payroll_record(payroll_data)
                if success:
                    generated_count += 1
                else:
                    errors.append(f"Failed to generate payroll for {employee['full_name']}")
            
            except Exception as e:
                errors.append(f"Error processing {employee['full_name']}: {str(e)}")
        
        # Show results
        message = f"Generated payroll for {generated_count} employees"
        if errors:
            message += f"\n\nErrors:\n" + "\n".join(errors[:5])
            if len(errors) > 5:
                message += f"\n... and {len(errors) - 5} more errors"
        
        messagebox.showinfo("Payroll Generation Complete", message)
        
        # Reload data
        self.load_payroll_data()

    def edit_payroll_record(self):
        """Edit selected payroll record"""
        selection = self.payroll_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a payroll record to edit")
            return

        # Get record ID
        item = self.payroll_tree.item(selection[0])
        record_id = item['values'][0]

        # Open edit dialog
        PayrollEditDialog(self.window, record_id, self.payroll_model, self.load_payroll_data)

    def pay_salary(self):
        """Process salary payment for selected employee"""
        selection = self.payroll_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select an employee to pay")
            return

        # Get record data
        item = self.payroll_tree.item(selection[0])
        record_id = item['values'][0]
        employee_name = item['values'][1]
        amount = item['values'][8]
        status = item['values'][9]

        if status == 'Paid':
            messagebox.showinfo("Already Paid", "This employee has already been paid for this period")
            return

        # Open payment dialog
        PaymentDialog(self.window, record_id, employee_name, amount,
                     self.payroll_model, self.session_manager, self.load_payroll_data)

    def generate_receipt(self):
        """Generate receipt for selected payment"""
        selection = self.payroll_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a paid record to generate receipt")
            return

        # Get record data
        item = self.payroll_tree.item(selection[0])
        record_id = item['values'][0]
        status = item['values'][9]

        if status != 'Paid':
            messagebox.showwarning("Not Paid", "Receipt can only be generated for paid records")
            return

        # Generate receipt
        try:
            from reports.receipt_generator import ReceiptGenerator

            record = self.payroll_model.get_payroll_record_by_id(record_id)
            if record:
                receipt_generator = ReceiptGenerator()
                receipt_path = receipt_generator.generate_receipt(record)
                messagebox.showinfo("Receipt Generated", f"Receipt saved to: {receipt_path}")
            else:
                messagebox.showerror("Error", "Payroll record not found")

        except ImportError:
            messagebox.showerror("Error", "Receipt generation module not available")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate receipt: {str(e)}")

    def export_payroll(self):
        """Export payroll data to Excel"""
        try:
            from utils.helpers import DataExporter
            import pandas as pd
            from tkinter import filedialog

            if not self.month_var.get() or not self.year_var.get():
                messagebox.showwarning("Missing Selection", "Please select month and year")
                return

            month = int(self.month_var.get().split(' - ')[0])
            year = int(self.year_var.get())

            # Get file path
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="Save Payroll Data",
                initialname=f"payroll_{year}_{month:02d}.xlsx"
            )

            if not file_path:
                return

            # Get payroll data
            records = self.payroll_model.get_payroll_records(month=month, year=year)
            export_data = DataExporter.prepare_payroll_data_for_export(records)

            # Create DataFrame and save
            df = pd.DataFrame(export_data)
            df.to_excel(file_path, index=False, sheet_name=f"Payroll_{year}_{month:02d}")

            messagebox.showinfo("Success", f"Payroll data exported to {file_path}")

        except ImportError:
            messagebox.showerror("Error", "pandas library is required for Excel export")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export data: {str(e)}")

    def load_employee_filter(self):
        """Load employees for history filter"""
        employees = self.employee_model.get_all_employees(active_only=False)
        employee_list = ["All"] + [emp['full_name'] for emp in employees]
        self.history_employee_combo['values'] = employee_list
        self.history_employee_combo.set("All")

    def load_payment_history(self):
        """Load payment history"""
        # Clear existing data
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)

        # Get all paid records
        records = self.payroll_model.get_payroll_records()
        paid_records = [r for r in records if r['payment_status'] == 'Paid']

        for record in paid_records:
            period = f"{record['pay_period_year']}-{record['pay_period_month']:02d}"
            self.history_tree.insert('', tk.END, values=(
                record['id'],
                record['full_name'],
                period,
                f"{record['total_amount']:,.2f}",
                record.get('payment_date', ''),
                record.get('payment_method', ''),
                record.get('paying_officer', ''),
                record.get('receipt_number', '')
            ))

    def filter_history(self):
        """Filter payment history based on criteria"""
        # This would implement filtering logic
        messagebox.showinfo("Filter", "History filtering would be implemented here")

    def view_receipt(self):
        """View receipt for selected payment"""
        selection = self.history_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a payment record")
            return

        messagebox.showinfo("View Receipt", "Receipt viewing would be implemented here")

    def reprint_receipt(self):
        """Reprint receipt for selected payment"""
        selection = self.history_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a payment record")
            return

        # Get record ID and regenerate receipt
        item = self.history_tree.item(selection[0])
        record_id = item['values'][0]

        try:
            from reports.receipt_generator import ReceiptGenerator

            record = self.payroll_model.get_payroll_record_by_id(record_id)
            if record:
                receipt_generator = ReceiptGenerator()
                receipt_path = receipt_generator.generate_receipt(record)
                messagebox.showinfo("Receipt Reprinted", f"Receipt saved to: {receipt_path}")
            else:
                messagebox.showerror("Error", "Payment record not found")

        except ImportError:
            messagebox.showerror("Error", "Receipt generation module not available")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to reprint receipt: {str(e)}")

    def export_history(self):
        """Export payment history to Excel"""
        try:
            import pandas as pd
            from tkinter import filedialog

            # Get file path
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="Save Payment History",
                initialname="payment_history.xlsx"
            )

            if not file_path:
                return

            # Get payment history data
            records = self.payroll_model.get_payroll_records()
            paid_records = [r for r in records if r['payment_status'] == 'Paid']

            # Prepare data for export
            export_data = []
            for record in paid_records:
                export_data.append({
                    'Employee Name': record['full_name'],
                    'Job Type': record['job_type'],
                    'Pay Period': f"{record['pay_period_year']}-{record['pay_period_month']:02d}",
                    'Amount Paid': record['total_amount'],
                    'Payment Date': record.get('payment_date', ''),
                    'Payment Method': record.get('payment_method', ''),
                    'Paying Officer': record.get('paying_officer', ''),
                    'Receipt Number': record.get('receipt_number', '')
                })

            # Create DataFrame and save
            df = pd.DataFrame(export_data)
            df.to_excel(file_path, index=False, sheet_name="Payment_History")

            messagebox.showinfo("Success", f"Payment history exported to {file_path}")

        except ImportError:
            messagebox.showerror("Error", "pandas library is required for Excel export")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export history: {str(e)}")

class PayrollEditDialog:
    """Dialog for editing payroll records"""
    def __init__(self, parent, record_id, payroll_model, refresh_callback):
        self.record_id = record_id
        self.payroll_model = payroll_model
        self.refresh_callback = refresh_callback

        # Load record data
        self.record = payroll_model.get_payroll_record_by_id(record_id)
        if not self.record:
            messagebox.showerror("Error", "Payroll record not found")
            return

        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"Edit Payroll - {self.record['full_name']}")
        self.dialog.geometry("500x400")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.create_widgets()

    def create_widgets(self):
        """Create edit dialog widgets"""
        # Main frame
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Employee info (read-only)
        info_frame = ttk.LabelFrame(main_frame, text="Employee Information", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(info_frame, text=f"Name: {self.record['full_name']}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"Job Type: {self.record['job_type']}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"Period: {self.record['pay_period_year']}-{self.record['pay_period_month']:02d}").pack(anchor=tk.W)

        # Editable fields
        edit_frame = ttk.LabelFrame(main_frame, text="Payroll Details", padding="10")
        edit_frame.pack(fill=tk.X, pady=(0, 10))

        edit_frame.columnconfigure(1, weight=1)

        row = 0

        # Base Salary
        ttk.Label(edit_frame, text="Base Salary:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.base_salary_var = tk.StringVar(value=str(self.record['base_salary']))
        ttk.Entry(edit_frame, textvariable=self.base_salary_var).grid(
            row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # Overtime Hours
        ttk.Label(edit_frame, text="Overtime Hours:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.overtime_hours_var = tk.StringVar(value=str(self.record.get('overtime_hours', 0)))
        ttk.Entry(edit_frame, textvariable=self.overtime_hours_var).grid(
            row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # Overtime Rate
        ttk.Label(edit_frame, text="Overtime Rate:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.overtime_rate_var = tk.StringVar(value=str(self.record.get('overtime_rate', 0)))
        ttk.Entry(edit_frame, textvariable=self.overtime_rate_var).grid(
            row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # Absence Days
        ttk.Label(edit_frame, text="Absence Days:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.absence_days_var = tk.StringVar(value=str(self.record.get('absence_days', 0)))
        ttk.Entry(edit_frame, textvariable=self.absence_days_var).grid(
            row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1

        # Calculate button
        ttk.Button(edit_frame, text="Calculate Total", command=self.calculate_total).grid(
            row=row, column=0, columnspan=2, pady=10)
        row += 1

        # Total Amount (calculated)
        ttk.Label(edit_frame, text="Total Amount:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.total_amount_var = tk.StringVar(value=str(self.record['total_amount']))
        total_entry = ttk.Entry(edit_frame, textvariable=self.total_amount_var, state='readonly')
        total_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # Buttons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(buttons_frame, text="Save", command=self.save_changes).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(buttons_frame, text="Cancel", command=self.dialog.destroy).pack(side=tk.RIGHT)

    def calculate_total(self):
        """Calculate total amount based on inputs"""
        try:
            base_salary = float(self.base_salary_var.get() or 0)
            overtime_hours = float(self.overtime_hours_var.get() or 0)
            overtime_rate = float(self.overtime_rate_var.get() or 0)
            absence_days = int(self.absence_days_var.get() or 0)

            # Calculate overtime amount
            overtime_amount = overtime_hours * overtime_rate

            # Calculate absence deduction
            absence_deduction = PayrollCalculator.calculate_absence_deduction(base_salary, absence_days)

            # Calculate total
            total_amount = base_salary + overtime_amount - absence_deduction

            self.total_amount_var.set(f"{total_amount:.2f}")

        except ValueError:
            messagebox.showerror("Error", "Please enter valid numeric values")

    def save_changes(self):
        """Save changes to payroll record"""
        try:
            # Validate inputs
            base_salary = float(self.base_salary_var.get() or 0)
            overtime_hours = float(self.overtime_hours_var.get() or 0)
            overtime_rate = float(self.overtime_rate_var.get() or 0)
            absence_days = int(self.absence_days_var.get() or 0)
            total_amount = float(self.total_amount_var.get() or 0)

            # Update record (this would require adding an update method to PayrollModel)
            messagebox.showinfo("Save", "Payroll record update functionality would be implemented here")

            # Close dialog and refresh
            self.dialog.destroy()
            if self.refresh_callback:
                self.refresh_callback()

        except ValueError:
            messagebox.showerror("Error", "Please enter valid numeric values")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save changes: {str(e)}")

class PaymentDialog:
    """Dialog for processing salary payments"""
    def __init__(self, parent, record_id, employee_name, amount, payroll_model, session_manager, refresh_callback):
        self.record_id = record_id
        self.employee_name = employee_name
        self.amount = amount
        self.payroll_model = payroll_model
        self.session_manager = session_manager
        self.refresh_callback = refresh_callback

        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"Pay Salary - {employee_name}")
        self.dialog.geometry("400x300")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.create_widgets()

    def create_widgets(self):
        """Create payment dialog widgets"""
        # Main frame
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Payment info
        info_frame = ttk.LabelFrame(main_frame, text="Payment Information", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(info_frame, text=f"Employee: {self.employee_name}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"Amount: {self.amount} QAR").pack(anchor=tk.W)

        # Payment details
        details_frame = ttk.LabelFrame(main_frame, text="Payment Details", padding="10")
        details_frame.pack(fill=tk.X, pady=(0, 10))

        details_frame.columnconfigure(1, weight=1)

        # Payment Date
        ttk.Label(details_frame, text="Payment Date:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.payment_date_var = tk.StringVar(value=date.today().strftime("%Y-%m-%d"))
        ttk.Entry(details_frame, textvariable=self.payment_date_var).grid(
            row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # Payment Method
        ttk.Label(details_frame, text="Payment Method:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.payment_method_var = tk.StringVar()
        method_combo = ttk.Combobox(details_frame, textvariable=self.payment_method_var,
                                   values=["Cash", "Bank Transfer", "Check"], state="readonly")
        method_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        method_combo.set("Cash")

        # Paying Officer
        ttk.Label(details_frame, text="Paying Officer:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.paying_officer_var = tk.StringVar()
        if self.session_manager.current_user:
            self.paying_officer_var.set(self.session_manager.current_user['full_name'])
        ttk.Entry(details_frame, textvariable=self.paying_officer_var).grid(
            row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # Buttons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(buttons_frame, text="Process Payment", command=self.process_payment).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(buttons_frame, text="Cancel", command=self.dialog.destroy).pack(side=tk.RIGHT)

    def process_payment(self):
        """Process the salary payment"""
        # Validate inputs
        if not self.payment_date_var.get():
            messagebox.showerror("Error", "Please enter payment date")
            return

        if not self.payment_method_var.get():
            messagebox.showerror("Error", "Please select payment method")
            return

        if not self.paying_officer_var.get():
            messagebox.showerror("Error", "Please enter paying officer name")
            return

        try:
            # Generate receipt number
            receipt_number = ReceiptHelper.generate_receipt_number(
                self.record_id,
                datetime.now().year,
                datetime.now().month
            )

            # Prepare payment data
            payment_data = {
                'payment_date': self.payment_date_var.get(),
                'payment_method': self.payment_method_var.get(),
                'paying_officer': self.paying_officer_var.get(),
                'receipt_number': receipt_number
            }

            # Update payment status
            success = self.payroll_model.update_payment_status(self.record_id, payment_data)

            if success:
                messagebox.showinfo("Success", f"Payment processed successfully!\nReceipt Number: {receipt_number}")

                # Close dialog and refresh
                self.dialog.destroy()
                if self.refresh_callback:
                    self.refresh_callback()
            else:
                messagebox.showerror("Error", "Failed to process payment")

        except Exception as e:
            messagebox.showerror("Error", f"An error occurred: {str(e)}")
