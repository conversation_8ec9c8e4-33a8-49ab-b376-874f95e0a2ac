#!/usr/bin/env python3
"""
Simple launcher for the Domestic Worker Payroll Management System
"""

import sys
import os
from pathlib import Path

def main():
    print("=" * 60)
    print("نظام إدارة رواتب العمالة المنزلية")
    print("Domestic Worker Payroll Management System")
    print("=" * 60)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print("❌ Error: Python 3.8 or newer required")
        input("اضغط Enter للخروج...")
        return
    
    print(f"✓ Python {sys.version.split()[0]}")
    
    # Check current directory
    current_dir = Path.cwd()
    print(f"✓ المجلد الحالي: {current_dir}")
    
    # Check if main.py exists
    main_file = current_dir / "main.py"
    if not main_file.exists():
        print("❌ خطأ: ملف main.py غير موجود")
        print("❌ Error: main.py file not found")
        input("اضغط Enter للخروج...")
        return
    
    print("✓ ملف main.py موجود")
    
    # Check required directories
    required_dirs = ["database", "ui", "utils", "reports"]
    for dir_name in required_dirs:
        if (current_dir / dir_name).exists():
            print(f"✓ مجلد {dir_name} موجود")
        else:
            print(f"❌ مجلد {dir_name} مفقود")
            input("اضغط Enter للخروج...")
            return
    
    # Test imports
    print("\n🔄 اختبار المكتبات المطلوبة...")
    try:
        import tkinter as tk
        print("✓ tkinter")
    except ImportError:
        print("❌ tkinter مفقود")
        input("اضغط Enter للخروج...")
        return
    
    try:
        import sqlite3
        print("✓ sqlite3")
    except ImportError:
        print("❌ sqlite3 مفقود")
        input("اضغط Enter للخروج...")
        return
    
    try:
        import bcrypt
        print("✓ bcrypt")
    except ImportError:
        print("❌ bcrypt مفقود - قم بتثبيته: pip install bcrypt")
        input("اضغط Enter للخروج...")
        return
    
    try:
        from reportlab.pdfgen import canvas
        print("✓ reportlab")
    except ImportError:
        print("❌ reportlab مفقود - قم بتثبيته: pip install reportlab")
        input("اضغط Enter للخروج...")
        return
    
    print("\n🚀 بدء تشغيل النظام...")
    
    # Import and run the main application
    try:
        # Add current directory to Python path
        sys.path.insert(0, str(current_dir))
        
        # Import main application
        from main import MainApplication
        
        # Create and run application
        app = MainApplication()
        print("✓ تم إنشاء التطبيق بنجاح")
        
        app.run()
        print("✓ تم إغلاق التطبيق بنجاح")
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        print("تأكد من وجود جميع الملفات المطلوبة")
        input("اضغط Enter للخروج...")
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        print("تفاصيل الخطأ:")
        import traceback
        traceback.print_exc()
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
