# Domestic Worker Payroll Management System

A comprehensive desktop application for managing domestic worker payroll, attendance, and administrative tasks built with Python and tkinter.

## Features

### Core Functionality
- **Employee Management**: Complete employee records with personal information, job details, and salary management
- **Payroll Processing**: Automated monthly payroll calculations with overtime, deductions, and allowances
- **Attendance Tracking**: Daily attendance recording with leave management and absence tracking
- **Receipt Generation**: Professional PDF receipts with company branding and detailed payment breakdowns
- **Financial Reporting**: Comprehensive reports including payment history, financial summaries, and analytics

### Security & Access Control
- **User Authentication**: Secure login system with password hashing
- **Role-Based Access**: Three user levels (Admin, Supervisor, Regular User) with appropriate permissions
- **Session Management**: Secure session handling with automatic logout

### Data Management
- **SQLite Database**: Reliable local database with proper relationships and constraints
- **Data Export**: Excel export functionality for all reports and data
- **Backup & Restore**: Database backup and restore capabilities
- **Data Validation**: Comprehensive input validation and error handling

## System Requirements

- **Operating System**: Windows 10/11, macOS 10.14+, or Linux
- **Python**: Python 3.8 or higher
- **Memory**: Minimum 4GB RAM recommended
- **Storage**: 100MB free disk space for application and database

## Installation

### 1. Clone or Download the Project
```bash
git clone <repository-url>
cd domestic-worker-payroll
```

### 2. Install Python Dependencies
```bash
pip install -r requirements.txt
```

### 3. Run the Application
```bash
python main.py
```

## First Time Setup

### 1. Default Admin Account
On first run, the system creates a default administrator account:
- **Username**: admin
- **Password**: admin123
- **Role**: Administrator

**Important**: Change the default password immediately after first login!

### 2. Initial Configuration
1. Login with the default admin account
2. Go to Tools → User Management to change the admin password
3. Create additional user accounts as needed
4. Add your first employees through Employees → Manage Employees

## User Guide

### Getting Started
1. **Login**: Use your credentials to access the system
2. **Navigation**: Use the toolbar buttons for quick access or menu bar for detailed options
3. **Employee Setup**: Add employees before processing payroll
4. **Payroll Processing**: Generate monthly payroll and process payments
5. **Reports**: Access comprehensive reports for analysis and record-keeping

### Main Modules

#### Employee Management
- Add, edit, and manage employee records
- Track personal information, job details, and salary information
- Export employee data to Excel
- Search and filter employee records

#### Payroll Processing
- Generate monthly payroll for all employees
- Process individual salary payments
- Generate and print payment receipts
- Track payment history and status

#### Attendance & Leave Management
- Record daily attendance for all employees
- Manage leave requests and approvals
- Track overtime and absence days
- Generate attendance reports

#### Reports & Analytics
- Payment history with advanced filtering
- Employee detailed reports
- Financial summaries and analytics
- Attendance and leave reports
- Export all reports to Excel

### User Roles & Permissions

#### Administrator
- Full system access
- User management
- System settings and maintenance
- Database backup and restore

#### Supervisor
- Employee management
- Payroll processing
- Leave approval
- Report generation

#### Regular User
- View employee information
- Basic reporting
- Limited data entry

## Database Structure

The system uses SQLite database with the following main tables:
- **users**: System user accounts and authentication
- **employees**: Employee personal and job information
- **payroll_records**: Monthly payroll calculations and payments
- **attendance_records**: Daily attendance tracking
- **leave_requests**: Leave applications and approvals

## Backup & Security

### Database Backup
- Use File → Backup Database to create backups
- Backups are stored in the `backups/` directory
- Regular backups are recommended

### Security Features
- Password hashing using bcrypt
- Session-based authentication
- Role-based access control
- Input validation and sanitization

## Troubleshooting

### Common Issues

#### Application Won't Start
- Ensure Python 3.8+ is installed
- Install all required dependencies: `pip install -r requirements.txt`
- Check for error messages in the console

#### Database Errors
- Ensure write permissions in the application directory
- Check if database file is corrupted
- Restore from backup if necessary

#### PDF Generation Issues
- Ensure reportlab is properly installed
- Check file permissions for receipt output directory
- Verify system fonts are accessible

### Getting Help
1. Check the error messages in the application
2. Review the console output for detailed error information
3. Ensure all dependencies are properly installed
4. Contact your system administrator for technical support

## Development

### Project Structure
```
domestic-worker-payroll/
├── main.py                 # Application entry point
├── database/
│   ├── models.py          # Database models and operations
│   └── payroll.db         # SQLite database file
├── ui/
│   ├── login.py           # Authentication and user management
│   ├── employee_management.py  # Employee CRUD operations
│   ├── payroll.py         # Payroll processing interface
│   ├── reports.py         # Reporting and analytics
│   └── attendance.py      # Attendance and leave management
├── utils/
│   ├── validators.py      # Input validation functions
│   ├── helpers.py         # Utility classes and functions
│   └── backup.py          # Database backup utilities
├── reports/
│   └── receipt_generator.py  # PDF receipt generation
├── receipts/              # Generated receipt files
├── backups/               # Database backup files
├── requirements.txt       # Python dependencies
└── README.md             # This file
```

### Adding New Features
1. Follow the existing code structure and patterns
2. Add proper error handling and validation
3. Update the database schema if needed
4. Test thoroughly with sample data
5. Update documentation as needed

## License

This project is developed for internal use. All rights reserved.

## Support

For technical support or questions about the system, contact your system administrator or the development team.

---

**Version**: 1.0  
**Last Updated**: 2024  
**Developed with**: Python 3.8+, tkinter, SQLite, reportlab
