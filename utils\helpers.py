import os
import shutil
from datetime import datetime, timedelta
from typing import Dict, Any, List
import calendar

class PayrollCalculator:
    @staticmethod
    def calculate_daily_rate(monthly_salary: float, working_days: int = 30) -> float:
        """Calculate daily rate from monthly salary"""
        return monthly_salary / working_days
    
    @staticmethod
    def calculate_absence_deduction(monthly_salary: float, absence_days: int, 
                                   working_days: int = 30) -> float:
        """Calculate deduction for absence days"""
        daily_rate = PayrollCalculator.calculate_daily_rate(monthly_salary, working_days)
        return daily_rate * absence_days
    
    @staticmethod
    def calculate_overtime_amount(overtime_hours: float, hourly_rate: float) -> float:
        """Calculate overtime amount"""
        return overtime_hours * hourly_rate
    
    @staticmethod
    def calculate_hourly_rate(monthly_salary: float, working_hours_per_month: int = 240) -> float:
        """Calculate hourly rate from monthly salary (assuming 8 hours/day, 30 days/month)"""
        return monthly_salary / working_hours_per_month
    
    @staticmethod
    def calculate_total_payroll(base_salary: float, overtime_amount: float = 0, 
                               absence_deduction: float = 0, other_deductions: float = 0,
                               bonuses: float = 0) -> float:
        """Calculate total payroll amount"""
        return base_salary + overtime_amount + bonuses - absence_deduction - other_deductions

class DateHelper:
    @staticmethod
    def get_month_name(month: int) -> str:
        """Get month name from month number"""
        return calendar.month_name[month]
    
    @staticmethod
    def get_working_days_in_month(year: int, month: int) -> int:
        """Get number of working days in a month (excluding Fridays and Saturdays)"""
        first_day = datetime(year, month, 1)
        if month == 12:
            last_day = datetime(year + 1, 1, 1) - timedelta(days=1)
        else:
            last_day = datetime(year, month + 1, 1) - timedelta(days=1)
        
        working_days = 0
        current_day = first_day
        
        while current_day <= last_day:
            # Friday = 4, Saturday = 5 (weekday() returns 0-6, Monday=0)
            if current_day.weekday() not in [4, 5]:  # Not Friday or Saturday
                working_days += 1
            current_day += timedelta(days=1)
        
        return working_days
    
    @staticmethod
    def get_date_range_for_month(year: int, month: int) -> tuple:
        """Get start and end date for a month"""
        start_date = datetime(year, month, 1).date()
        if month == 12:
            end_date = datetime(year + 1, 1, 1).date() - timedelta(days=1)
        else:
            end_date = datetime(year, month + 1, 1).date() - timedelta(days=1)
        
        return start_date, end_date
    
    @staticmethod
    def format_date(date_obj, format_str: str = "%Y-%m-%d") -> str:
        """Format date object to string"""
        if isinstance(date_obj, str):
            return date_obj
        return date_obj.strftime(format_str)
    
    @staticmethod
    def parse_date(date_str: str, format_str: str = "%Y-%m-%d") -> datetime:
        """Parse date string to datetime object"""
        return datetime.strptime(date_str, format_str)

class FileHelper:
    @staticmethod
    def ensure_directory_exists(directory_path: str):
        """Ensure directory exists, create if it doesn't"""
        os.makedirs(directory_path, exist_ok=True)
    
    @staticmethod
    def backup_file(source_path: str, backup_dir: str = "backups") -> str:
        """Create a backup of a file"""
        FileHelper.ensure_directory_exists(backup_dir)
        
        filename = os.path.basename(source_path)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"{timestamp}_{filename}"
        backup_path = os.path.join(backup_dir, backup_filename)
        
        shutil.copy2(source_path, backup_path)
        return backup_path
    
    @staticmethod
    def get_file_size(file_path: str) -> int:
        """Get file size in bytes"""
        return os.path.getsize(file_path) if os.path.exists(file_path) else 0
    
    @staticmethod
    def clean_filename(filename: str) -> str:
        """Clean filename by removing invalid characters"""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        return filename

class ReceiptHelper:
    @staticmethod
    def generate_receipt_number(employee_id: int, year: int, month: int) -> str:
        """Generate unique receipt number"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        return f"RCP-{employee_id:04d}-{year}{month:02d}-{timestamp[-6:]}"
    
    @staticmethod
    def format_currency(amount: float, currency: str = "QAR") -> str:
        """Format currency amount"""
        return f"{amount:,.2f} {currency}"
    
    @staticmethod
    def number_to_words(amount: float) -> str:
        """Convert number to words (basic implementation)"""
        # This is a simplified version - you might want to use a library like num2words
        ones = ["", "one", "two", "three", "four", "five", "six", "seven", "eight", "nine"]
        teens = ["ten", "eleven", "twelve", "thirteen", "fourteen", "fifteen", 
                "sixteen", "seventeen", "eighteen", "nineteen"]
        tens = ["", "", "twenty", "thirty", "forty", "fifty", "sixty", "seventy", "eighty", "ninety"]
        
        def convert_hundreds(n):
            result = ""
            if n >= 100:
                result += ones[n // 100] + " hundred "
                n %= 100
            if n >= 20:
                result += tens[n // 10] + " "
                n %= 10
            elif n >= 10:
                result += teens[n - 10] + " "
                n = 0
            if n > 0:
                result += ones[n] + " "
            return result
        
        if amount == 0:
            return "zero"
        
        integer_part = int(amount)
        decimal_part = int((amount - integer_part) * 100)
        
        result = ""
        
        if integer_part >= 1000:
            thousands = integer_part // 1000
            result += convert_hundreds(thousands) + "thousand "
            integer_part %= 1000
        
        result += convert_hundreds(integer_part)
        
        if decimal_part > 0:
            result += "and " + str(decimal_part) + "/100"
        
        return result.strip().title()

class DataExporter:
    @staticmethod
    def prepare_employee_data_for_export(employees: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Prepare employee data for Excel export"""
        export_data = []
        for emp in employees:
            export_data.append({
                'ID': emp['id'],
                'Full Name': emp['full_name'],
                'Nationality': emp['nationality'],
                'ID/Residence Number': emp['id_residence_number'],
                'Job Type': emp['job_type'],
                'Monthly Salary': emp['monthly_salary'],
                'Start Date': emp['start_date'],
                'End Date': emp.get('end_date', ''),
                'Status': 'Active' if emp['is_active'] else 'Inactive',
                'Notes': emp.get('additional_notes', '')
            })
        return export_data
    
    @staticmethod
    def prepare_payroll_data_for_export(payroll_records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Prepare payroll data for Excel export"""
        export_data = []
        for record in payroll_records:
            export_data.append({
                'Employee Name': record['full_name'],
                'Job Type': record['job_type'],
                'Month': record['pay_period_month'],
                'Year': record['pay_period_year'],
                'Base Salary': record['base_salary'],
                'Overtime Hours': record.get('overtime_hours', 0),
                'Overtime Amount': record.get('overtime_amount', 0),
                'Absence Days': record.get('absence_days', 0),
                'Absence Deduction': record.get('absence_deduction', 0),
                'Total Amount': record['total_amount'],
                'Payment Status': record['payment_status'],
                'Payment Date': record.get('payment_date', ''),
                'Payment Method': record.get('payment_method', ''),
                'Receipt Number': record.get('receipt_number', '')
            })
        return export_data
