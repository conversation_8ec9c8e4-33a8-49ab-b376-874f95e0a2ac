========================================
نظام إدارة رواتب العمالة المنزلية
دليل التشغيل السريع
========================================

🔧 خطوات التشغيل:

1. تثبيت المكتبات المطلوبة:
   - انقر نقراً مزدوجاً على ملف: install_requirements.bat
   - انتظر حتى انتهاء التثبيت

2. تشغيل النظام:
   - انقر نقراً مزدوجاً على ملف: run_system.bat
   - أو انقر نقراً مزدوجاً على ملف: run_system.py

3. تسجيل الدخول:
   - اسم المستخدم: admin
   - كلمة المرور: admin123

========================================

🚨 في حالة عدم عمل النظام:

1. تأكد من تثبيت Python:
   - قم بتحميل Python من: https://python.org
   - اختر إصدار 3.8 أو أحدث
   - تأكد من تحديد "Add Python to PATH" أثناء التثبيت

2. تشغيل من سطر الأوامر:
   - افتح Command Prompt
   - انتقل إلى مجلد النظام
   - اكتب: python run_system.py

3. تثبيت المكتبات يدوياً:
   - pip install bcrypt
   - pip install pandas
   - pip install openpyxl
   - pip install reportlab
   - pip install Pillow
   - pip install python-dateutil

========================================

📋 ميزات النظام:

✓ إدارة الموظفين
✓ معالجة الرواتب الشهرية
✓ تتبع الحضور والغياب
✓ إدارة الإجازات
✓ إنتاج الإيصالات PDF
✓ التقارير الشاملة
✓ تصدير البيانات إلى Excel
✓ نسخ احتياطي للبيانات

========================================

🔐 أدوار المستخدمين:

1. المدير (Admin):
   - جميع الصلاحيات
   - إدارة المستخدمين
   - إعدادات النظام

2. المشرف (Supervisor):
   - إدارة الموظفين
   - معالجة الرواتب
   - عرض التقارير

3. المستخدم العادي (Regular User):
   - عرض البيانات فقط
   - طباعة التقارير

========================================

📞 للدعم الفني:
في حالة وجود مشاكل، يرجى التواصل مع مطور النظام
وإرفاق رسالة الخطأ إن وجدت.

========================================
