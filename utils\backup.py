import os
import shutil
import sqlite3
import zipfile
from datetime import datetime
from typing import Optional, List
import json

class DatabaseBackup:
    def __init__(self, db_path: str = "database/payroll.db"):
        self.db_path = db_path
        self.backup_dir = "backups"
        self.ensure_backup_directory()
    
    def ensure_backup_directory(self):
        """Ensure backup directory exists"""
        os.makedirs(self.backup_dir, exist_ok=True)
    
    def create_backup(self, backup_name: Optional[str] = None) -> str:
        """Create a complete database backup"""
        if not backup_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"payroll_backup_{timestamp}"
        
        backup_path = os.path.join(self.backup_dir, f"{backup_name}.zip")
        
        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # Add database file
            if os.path.exists(self.db_path):
                zipf.write(self.db_path, "payroll.db")
            
            # Add metadata
            metadata = {
                "backup_date": datetime.now().isoformat(),
                "database_path": self.db_path,
                "backup_type": "full"
            }
            zipf.writestr("backup_metadata.json", json.dumps(metadata, indent=2))
        
        return backup_path
    
    def restore_backup(self, backup_path: str) -> bool:
        """Restore database from backup"""
        try:
            # Create backup of current database before restore
            if os.path.exists(self.db_path):
                current_backup = self.create_backup("pre_restore_backup")
                print(f"Current database backed up to: {current_backup}")
            
            # Extract backup
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                # Extract database file
                zipf.extract("payroll.db", os.path.dirname(self.db_path))
                
                # Move to correct location
                extracted_path = os.path.join(os.path.dirname(self.db_path), "payroll.db")
                if extracted_path != self.db_path:
                    shutil.move(extracted_path, self.db_path)
            
            return True
        except Exception as e:
            print(f"Error restoring backup: {e}")
            return False
    
    def list_backups(self) -> List[dict]:
        """List all available backups"""
        backups = []
        
        if not os.path.exists(self.backup_dir):
            return backups
        
        for filename in os.listdir(self.backup_dir):
            if filename.endswith('.zip'):
                backup_path = os.path.join(self.backup_dir, filename)
                try:
                    with zipfile.ZipFile(backup_path, 'r') as zipf:
                        if "backup_metadata.json" in zipf.namelist():
                            metadata_content = zipf.read("backup_metadata.json").decode('utf-8')
                            metadata = json.loads(metadata_content)
                        else:
                            # Fallback for backups without metadata
                            metadata = {
                                "backup_date": datetime.fromtimestamp(
                                    os.path.getctime(backup_path)
                                ).isoformat(),
                                "backup_type": "unknown"
                            }
                    
                    backup_info = {
                        "filename": filename,
                        "path": backup_path,
                        "size": os.path.getsize(backup_path),
                        "created_date": metadata.get("backup_date", "Unknown"),
                        "backup_type": metadata.get("backup_type", "unknown")
                    }
                    backups.append(backup_info)
                
                except Exception as e:
                    print(f"Error reading backup {filename}: {e}")
        
        # Sort by creation date (newest first)
        backups.sort(key=lambda x: x["created_date"], reverse=True)
        return backups
    
    def delete_backup(self, backup_filename: str) -> bool:
        """Delete a backup file"""
        try:
            backup_path = os.path.join(self.backup_dir, backup_filename)
            if os.path.exists(backup_path):
                os.remove(backup_path)
                return True
            return False
        except Exception as e:
            print(f"Error deleting backup: {e}")
            return False
    
    def export_data_to_sql(self, output_path: str) -> bool:
        """Export database to SQL file"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                # Write SQL dump
                for line in conn.iterdump():
                    f.write(f"{line}\n")
            
            conn.close()
            return True
        except Exception as e:
            print(f"Error exporting to SQL: {e}")
            return False
    
    def import_data_from_sql(self, sql_file_path: str) -> bool:
        """Import data from SQL file"""
        try:
            # Create backup before import
            backup_path = self.create_backup("pre_import_backup")
            print(f"Database backed up to: {backup_path}")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Read and execute SQL file
            with open(sql_file_path, 'r', encoding='utf-8') as f:
                sql_script = f.read()
            
            cursor.executescript(sql_script)
            conn.commit()
            conn.close()
            
            return True
        except Exception as e:
            print(f"Error importing from SQL: {e}")
            return False
    
    def cleanup_old_backups(self, keep_count: int = 10) -> int:
        """Clean up old backups, keeping only the specified number"""
        backups = self.list_backups()
        
        if len(backups) <= keep_count:
            return 0
        
        deleted_count = 0
        backups_to_delete = backups[keep_count:]
        
        for backup in backups_to_delete:
            if self.delete_backup(backup["filename"]):
                deleted_count += 1
        
        return deleted_count
    
    def verify_backup(self, backup_path: str) -> bool:
        """Verify backup integrity"""
        try:
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                # Check if required files exist
                if "payroll.db" not in zipf.namelist():
                    return False
                
                # Test extraction
                zipf.testzip()
                
                # Try to open the database file
                with zipf.open("payroll.db") as db_file:
                    # Write to temporary file and test
                    temp_db_path = "temp_verify.db"
                    with open(temp_db_path, 'wb') as temp_file:
                        temp_file.write(db_file.read())
                    
                    # Test database connection
                    conn = sqlite3.connect(temp_db_path)
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                    tables = cursor.fetchall()
                    conn.close()
                    
                    # Clean up
                    os.remove(temp_db_path)
                    
                    # Check if essential tables exist
                    essential_tables = ['users', 'employees', 'payroll_records']
                    table_names = [table[0] for table in tables]
                    
                    for table in essential_tables:
                        if table not in table_names:
                            return False
                
                return True
        
        except Exception as e:
            print(f"Error verifying backup: {e}")
            return False

class DataExportImport:
    def __init__(self, db_manager):
        self.db = db_manager
    
    def export_to_excel(self, output_path: str, include_tables: List[str] = None) -> bool:
        """Export database tables to Excel file"""
        try:
            import pandas as pd
            
            conn = self.db.get_connection()
            
            if include_tables is None:
                include_tables = ['employees', 'payroll_records', 'attendance_records', 'leave_requests']
            
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                for table in include_tables:
                    try:
                        df = pd.read_sql_query(f"SELECT * FROM {table}", conn)
                        df.to_excel(writer, sheet_name=table, index=False)
                    except Exception as e:
                        print(f"Error exporting table {table}: {e}")
            
            conn.close()
            return True
        
        except Exception as e:
            print(f"Error exporting to Excel: {e}")
            return False
    
    def import_from_excel(self, excel_path: str, table_mappings: dict = None) -> bool:
        """Import data from Excel file"""
        try:
            import pandas as pd
            
            if table_mappings is None:
                table_mappings = {
                    'employees': 'employees',
                    'payroll_records': 'payroll_records',
                    'attendance_records': 'attendance_records'
                }
            
            conn = self.db.get_connection()
            
            for sheet_name, table_name in table_mappings.items():
                try:
                    df = pd.read_excel(excel_path, sheet_name=sheet_name)
                    df.to_sql(table_name, conn, if_exists='append', index=False)
                except Exception as e:
                    print(f"Error importing sheet {sheet_name}: {e}")
            
            conn.commit()
            conn.close()
            return True
        
        except Exception as e:
            print(f"Error importing from Excel: {e}")
            return False
