@echo off
chcp 65001 >nul
title نظام إدارة رواتب العمالة المنزلية

echo ========================================
echo نظام إدارة رواتب العمالة المنزلية
echo Domestic Worker Payroll Management System
echo ========================================
echo.

echo 🔄 التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث من python.org
    pause
    exit /b 1
)

echo ✓ Python مثبت
echo.

echo 🔄 التحقق من المكتبات المطلوبة...
python -c "import tkinter" >nul 2>&1
if errorlevel 1 (
    echo ❌ tkinter غير متوفر
    pause
    exit /b 1
)
echo ✓ tkinter

python -c "import sqlite3" >nul 2>&1
if errorlevel 1 (
    echo ❌ sqlite3 غير متوفر
    pause
    exit /b 1
)
echo ✓ sqlite3

python -c "import bcrypt" >nul 2>&1
if errorlevel 1 (
    echo ❌ bcrypt غير مثبت
    echo 🔄 تثبيت bcrypt...
    pip install bcrypt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت bcrypt
        pause
        exit /b 1
    )
)
echo ✓ bcrypt

python -c "import reportlab" >nul 2>&1
if errorlevel 1 (
    echo ❌ reportlab غير مثبت
    echo 🔄 تثبيت reportlab...
    pip install reportlab
    if errorlevel 1 (
        echo ❌ فشل في تثبيت reportlab
        pause
        exit /b 1
    )
)
echo ✓ reportlab

echo.
echo 🚀 بدء تشغيل النظام...
echo.

python run_system.py

echo.
echo تم الانتهاء من تشغيل النظام
pause
