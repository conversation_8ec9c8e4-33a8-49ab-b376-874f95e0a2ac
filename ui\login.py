import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Dict, Any, Callable
import os

class SessionManager:
    """Manage user session data"""
    def __init__(self):
        self.current_user: Optional[Dict[str, Any]] = None
        self.session_active = False
    
    def login(self, user_data: Dict[str, Any]):
        """Start user session"""
        self.current_user = user_data
        self.session_active = True
    
    def logout(self):
        """End user session"""
        self.current_user = None
        self.session_active = False
    
    def is_authenticated(self) -> bool:
        """Check if user is authenticated"""
        return self.session_active and self.current_user is not None
    
    def has_permission(self, required_role: str) -> bool:
        """Check if current user has required role"""
        if not self.is_authenticated():
            return False
        
        user_role = self.current_user.get('role', '')
        
        # Role hierarchy: Admin > Supervisor > Regular User
        role_hierarchy = {
            'Admin': 3,
            'Supervisor': 2,
            'Regular User': 1
        }
        
        user_level = role_hierarchy.get(user_role, 0)
        required_level = role_hierarchy.get(required_role, 0)
        
        return user_level >= required_level
    
    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """Get current user information"""
        return self.current_user

class LoginWindow:
    def __init__(self, user_model, session_manager: SessionManager, 
                 on_success_callback: Callable = None):
        self.user_model = user_model
        self.session_manager = session_manager
        self.on_success_callback = on_success_callback
        
        self.root = tk.Tk()
        self.root.title("Domestic Worker Payroll System - Login")
        self.root.geometry("400x300")
        self.root.resizable(False, False)
        
        # Center the window
        self.center_window()
        
        # Configure style
        self.setup_styles()
        
        # Create UI
        self.create_widgets()
        
        # Bind Enter key to login
        self.root.bind('<Return>', lambda event: self.login())

        # Handle window close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Focus on username field
        self.username_entry.focus()
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_styles(self):
        """Setup UI styles"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure colors
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), foreground='#2c3e50')
        style.configure('Login.TButton', font=('Arial', 10, 'bold'))
    
    def create_widgets(self):
        """Create and arrange UI widgets"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Payroll Management System", 
                               style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Logo/Icon placeholder
        logo_frame = ttk.Frame(main_frame)
        logo_frame.grid(row=1, column=0, columnspan=2, pady=(0, 20))
        
        # You can add a logo here if available
        logo_label = ttk.Label(logo_frame, text="🏢", font=('Arial', 24))
        logo_label.pack()
        
        # Username field
        ttk.Label(main_frame, text="Username:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.username_entry = ttk.Entry(main_frame, width=25, font=('Arial', 10))
        self.username_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # Password field
        ttk.Label(main_frame, text="Password:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.password_entry = ttk.Entry(main_frame, width=25, show="*", font=('Arial', 10))
        self.password_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # Remember me checkbox
        self.remember_var = tk.BooleanVar()
        remember_check = ttk.Checkbutton(main_frame, text="Remember me", 
                                        variable=self.remember_var)
        remember_check.grid(row=4, column=1, sticky=tk.W, pady=10, padx=(10, 0))
        
        # Login button
        login_button = ttk.Button(main_frame, text="Login", command=self.login,
                                 style='Login.TButton')
        login_button.grid(row=5, column=0, columnspan=2, pady=20, sticky=(tk.W, tk.E))
        
        # Status label
        self.status_label = ttk.Label(main_frame, text="", foreground='red')
        self.status_label.grid(row=6, column=0, columnspan=2, pady=5)
        
        # Default credentials info
        info_frame = ttk.LabelFrame(main_frame, text="Default Login", padding="10")
        info_frame.grid(row=7, column=0, columnspan=2, pady=20, sticky=(tk.W, tk.E))
        
        ttk.Label(info_frame, text="Username: admin").pack(anchor=tk.W)
        ttk.Label(info_frame, text="Password: admin123").pack(anchor=tk.W)
    
    def login(self):
        """Handle login attempt"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        
        # Clear previous status
        self.status_label.config(text="")
        
        # Validate input
        if not username:
            self.show_error("Please enter username")
            self.username_entry.focus()
            return
        
        if not password:
            self.show_error("Please enter password")
            self.password_entry.focus()
            return
        
        # Attempt authentication
        try:
            user_data = self.user_model.authenticate(username, password)
            
            if user_data:
                # Successful login
                self.session_manager.login(user_data)
                
                # Save credentials if remember me is checked
                if self.remember_var.get():
                    self.save_credentials(username)
                
                # Close login window
                self.root.destroy()
                
                # Call success callback
                if self.on_success_callback:
                    self.on_success_callback()
            else:
                self.show_error("Invalid username or password")
                self.password_entry.delete(0, tk.END)
                self.password_entry.focus()
        
        except Exception as e:
            self.show_error(f"Login error: {str(e)}")
    
    def show_error(self, message: str):
        """Show error message"""
        self.status_label.config(text=message, foreground='red')

    def on_closing(self):
        """Handle window close event"""
        # Close the login window without logging in
        self.root.destroy()
    
    def save_credentials(self, username: str):
        """Save username for remember me feature"""
        try:
            with open("last_user.txt", "w") as f:
                f.write(username)
        except Exception:
            pass  # Ignore errors in saving credentials
    
    def load_saved_credentials(self):
        """Load saved username if available"""
        try:
            if os.path.exists("last_user.txt"):
                with open("last_user.txt", "r") as f:
                    username = f.read().strip()
                    if username:
                        self.username_entry.insert(0, username)
                        self.remember_var.set(True)
                        self.password_entry.focus()
        except Exception:
            pass  # Ignore errors in loading credentials
    
    def show(self):
        """Show the login window"""
        # Load saved credentials
        self.load_saved_credentials()
        
        # Start the main loop
        self.root.mainloop()

class UserManagementDialog:
    """Dialog for managing users (Admin only)"""
    def __init__(self, parent, user_model, session_manager: SessionManager):
        self.user_model = user_model
        self.session_manager = session_manager
        
        # Check permissions
        if not session_manager.has_permission('Admin'):
            messagebox.showerror("Access Denied", "Only administrators can manage users")
            return
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("User Management")
        self.dialog.geometry("600x400")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.create_widgets()
        self.load_users()
    
    def create_widgets(self):
        """Create user management interface"""
        # Main frame
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Users list
        list_frame = ttk.LabelFrame(main_frame, text="Users", padding="5")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Treeview for users
        columns = ('ID', 'Username', 'Full Name', 'Role', 'Created')
        self.users_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.users_tree.heading(col, text=col)
            self.users_tree.column(col, width=100)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=scrollbar.set)
        
        self.users_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)
        
        ttk.Button(buttons_frame, text="Add User", command=self.add_user).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="Edit User", command=self.edit_user).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Delete User", command=self.delete_user).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Close", command=self.dialog.destroy).pack(side=tk.RIGHT)
    
    def load_users(self):
        """Load users into the treeview"""
        # Clear existing items
        for item in self.users_tree.get_children():
            self.users_tree.delete(item)
        
        # Load users
        users = self.user_model.get_all_users()
        for user in users:
            self.users_tree.insert('', tk.END, values=(
                user['id'],
                user['username'],
                user['full_name'],
                user['role'],
                user['created_at'][:10]  # Show only date part
            ))
    
    def add_user(self):
        """Add new user"""
        # This would open a user creation dialog
        messagebox.showinfo("Add User", "User creation dialog would open here")
    
    def edit_user(self):
        """Edit selected user"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a user to edit")
            return
        
        messagebox.showinfo("Edit User", "User editing dialog would open here")
    
    def delete_user(self):
        """Delete selected user"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a user to delete")
            return
        
        if messagebox.askyesno("Confirm Delete", "Are you sure you want to delete this user?"):
            messagebox.showinfo("Delete User", "User deletion would be implemented here")
