#!/usr/bin/env python3
"""
Test Script for Domestic Worker Payroll Management System

This script performs basic tests to ensure the system components are working correctly.
It tests database connectivity, model operations, and basic functionality.

Run this script to verify the system is properly set up before first use.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test that all required modules can be imported"""
    print("Testing module imports...")
    
    try:
        from database.models import DatabaseManager, UserModel, EmployeeModel, PayrollModel, AttendanceModel, LeaveModel
        print("✓ Database models imported successfully")
        
        from ui.login import SessionManager, LoginWindow
        print("✓ Login module imported successfully")
        
        from ui.employee_management import EmployeeManagementWindow
        print("✓ Employee management module imported successfully")
        
        from ui.payroll import PayrollProcessingWindow
        print("✓ Payroll module imported successfully")
        
        from ui.reports import ReportsWindow
        print("✓ Reports module imported successfully")
        
        from ui.attendance import AttendanceManagementWindow
        print("✓ Attendance module imported successfully")
        
        from utils.validators import Validators
        from utils.helpers import PayrollCalculator, DateHelper, FileHelper
        print("✓ Utility modules imported successfully")
        
        from reports.receipt_generator import ReceiptGenerator
        print("✓ Receipt generator imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def test_dependencies():
    """Test that all required dependencies are available"""
    print("\nTesting dependencies...")
    
    dependencies = [
        ('bcrypt', 'Password hashing'),
        ('pandas', 'Data processing'),
        ('openpyxl', 'Excel export'),
        ('reportlab', 'PDF generation'),
        ('tkinter', 'GUI framework')
    ]
    
    all_available = True
    
    for module_name, description in dependencies:
        try:
            if module_name == 'tkinter':
                import tkinter
            else:
                __import__(module_name)
            print(f"✓ {module_name} - {description}")
        except ImportError:
            print(f"✗ {module_name} - {description} (MISSING)")
            all_available = False
    
    return all_available

def test_database():
    """Test database initialization and basic operations"""
    print("\nTesting database...")
    
    try:
        from database.models import DatabaseManager, UserModel
        
        # Initialize database
        db_manager = DatabaseManager()
        print("✓ Database manager initialized")
        
        # Test user model
        user_model = UserModel(db_manager)
        print("✓ User model created")
        
        # Test creating default admin user
        admin_data = {
            'username': 'test_admin',
            'password': 'test123',
            'full_name': 'Test Administrator',
            'role': 'Admin',
            'email': '<EMAIL>'
        }
        
        # Clean up any existing test user
        existing_user = user_model.get_user_by_username('test_admin')
        if existing_user:
            # In a real implementation, you'd have a delete method
            print("✓ Test user already exists (cleanup needed)")
        else:
            user_id = user_model.create_user(admin_data)
            if user_id:
                print("✓ Test user created successfully")
                
                # Test authentication
                authenticated_user = user_model.authenticate_user('test_admin', 'test123')
                if authenticated_user:
                    print("✓ User authentication working")
                else:
                    print("✗ User authentication failed")
            else:
                print("✗ Failed to create test user")
        
        db_manager.close()
        return True
        
    except Exception as e:
        print(f"✗ Database test failed: {e}")
        return False

def test_file_structure():
    """Test that required directories and files exist"""
    print("\nTesting file structure...")
    
    required_dirs = [
        'database',
        'ui',
        'utils',
        'reports'
    ]
    
    required_files = [
        'main.py',
        'requirements.txt',
        'database/models.py',
        'ui/login.py',
        'ui/employee_management.py',
        'ui/payroll.py',
        'ui/reports.py',
        'ui/attendance.py',
        'utils/validators.py',
        'utils/helpers.py',
        'utils/backup.py',
        'reports/receipt_generator.py'
    ]
    
    all_present = True
    
    # Check directories
    for dir_name in required_dirs:
        if os.path.exists(dir_name) and os.path.isdir(dir_name):
            print(f"✓ Directory: {dir_name}")
        else:
            print(f"✗ Directory missing: {dir_name}")
            all_present = False
    
    # Check files
    for file_name in required_files:
        if os.path.exists(file_name) and os.path.isfile(file_name):
            print(f"✓ File: {file_name}")
        else:
            print(f"✗ File missing: {file_name}")
            all_present = False
    
    return all_present

def test_utilities():
    """Test utility functions"""
    print("\nTesting utilities...")
    
    try:
        from utils.validators import Validators
        from utils.helpers import PayrollCalculator, DateHelper
        
        # Test validators
        if Validators.validate_email("<EMAIL>"):
            print("✓ Email validation working")
        else:
            print("✗ Email validation failed")
        
        if Validators.validate_phone("+974-1234-5678"):
            print("✓ Phone validation working")
        else:
            print("✗ Phone validation failed")
        
        # Test payroll calculator
        base_salary = 3000.0
        overtime_hours = 5.0
        overtime_amount = PayrollCalculator.calculate_overtime(base_salary, overtime_hours)
        if overtime_amount > 0:
            print(f"✓ Overtime calculation working: {overtime_amount} QAR")
        else:
            print("✗ Overtime calculation failed")
        
        # Test date helper
        from datetime import date
        working_days = DateHelper.get_working_days_in_month(2024, 1)
        if working_days > 0:
            print(f"✓ Working days calculation: {working_days} days")
        else:
            print("✗ Working days calculation failed")
        
        return True
        
    except Exception as e:
        print(f"✗ Utilities test failed: {e}")
        return False

def create_sample_data():
    """Create sample data for testing"""
    print("\nCreating sample data...")
    
    try:
        from database.models import DatabaseManager, UserModel, EmployeeModel
        
        db_manager = DatabaseManager()
        user_model = UserModel(db_manager)
        employee_model = EmployeeModel(db_manager)
        
        # Create sample employee
        sample_employee = {
            'full_name': 'Ahmed Hassan',
            'nationality': 'Egyptian',
            'passport_number': 'A1234567',
            'visa_number': 'V7654321',
            'phone': '+974-1234-5678',
            'email': '<EMAIL>',
            'address': '123 Test Street, Doha',
            'job_type': 'House Cleaner',
            'monthly_salary': 2500.0,
            'hire_date': '2024-01-01',
            'contract_end_date': '2025-01-01',
            'is_active': True
        }
        
        employee_id = employee_model.create_employee(sample_employee)
        if employee_id:
            print("✓ Sample employee created")
        else:
            print("✗ Failed to create sample employee")
        
        db_manager.close()
        return True
        
    except Exception as e:
        print(f"✗ Sample data creation failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("DOMESTIC WORKER PAYROLL MANAGEMENT SYSTEM - SYSTEM TEST")
    print("=" * 60)
    
    tests = [
        ("Module Imports", test_imports),
        ("Dependencies", test_dependencies),
        ("File Structure", test_file_structure),
        ("Database", test_database),
        ("Utilities", test_utilities),
        ("Sample Data", create_sample_data)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"Running: {test_name}")
        print(f"{'-' * 40}")
        
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} PASSED")
            else:
                print(f"✗ {test_name} FAILED")
        except Exception as e:
            print(f"✗ {test_name} ERROR: {e}")
    
    print(f"\n{'=' * 60}")
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    print(f"{'=' * 60}")
    
    if passed == total:
        print("🎉 All tests passed! The system is ready to use.")
        print("\nNext steps:")
        print("1. Run 'python main.py' to start the application")
        print("2. Login with username: admin, password: admin123")
        print("3. Change the default admin password")
        print("4. Start adding your employees and processing payroll")
    else:
        print("⚠️  Some tests failed. Please fix the issues before using the system.")
        print("\nCommon solutions:")
        print("1. Install missing dependencies: pip install -r requirements.txt")
        print("2. Ensure all files are present and accessible")
        print("3. Check file permissions")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
