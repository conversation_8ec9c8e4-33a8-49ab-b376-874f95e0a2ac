import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
from typing import Dict, Any, Optional
from utils.validators import Validators

class EmployeeManagementWindow:
    def __init__(self, parent, employee_model, session_manager):
        self.employee_model = employee_model
        self.session_manager = session_manager
        self.parent = parent
        
        # Check permissions
        if not session_manager.has_permission('Supervisor'):
            messagebox.showerror("Access Denied", "Insufficient permissions to manage employees")
            return
        
        self.window = tk.Toplevel(parent)
        self.window.title("Employee Management")
        self.window.geometry("1000x600")
        self.window.transient(parent)
        
        # Job types
        self.job_types = [
            "Maid", "<PERSON>", "Driver", "Gardener", "Nanny", "Cleaner", 
            "Security Guard", "Maintenance", "Other"
        ]
        
        # Nationalities (common ones for domestic workers)
        self.nationalities = [
            "Filipino", "Indonesian", "Sri Lankan", "Indian", "Bangladeshi",
            "Pakistani", "Nepalese", "Ethiopian", "Kenyan", "Other"
        ]
        
        self.create_widgets()
        self.load_employees()
    
    def create_widgets(self):
        """Create the employee management interface"""
        # Create notebook for tabs
        notebook = ttk.Notebook(self.window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Employee List Tab
        self.list_frame = ttk.Frame(notebook)
        notebook.add(self.list_frame, text="Employee List")
        self.create_list_tab()
        
        # Add/Edit Employee Tab
        self.form_frame = ttk.Frame(notebook)
        notebook.add(self.form_frame, text="Add/Edit Employee")
        self.create_form_tab()
    
    def create_list_tab(self):
        """Create employee list tab"""
        # Search frame
        search_frame = ttk.Frame(self.list_frame)
        search_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(search_frame, text="Search:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.filter_employees)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(5, 10))
        
        # Filter by job type
        ttk.Label(search_frame, text="Job Type:").pack(side=tk.LEFT)
        self.job_filter_var = tk.StringVar()
        self.job_filter_var.trace('w', self.filter_employees)
        job_filter = ttk.Combobox(search_frame, textvariable=self.job_filter_var, 
                                 values=["All"] + self.job_types, state="readonly", width=15)
        job_filter.set("All")
        job_filter.pack(side=tk.LEFT, padx=(5, 10))
        
        # Active/Inactive filter
        ttk.Label(search_frame, text="Status:").pack(side=tk.LEFT)
        self.status_filter_var = tk.StringVar()
        self.status_filter_var.trace('w', self.filter_employees)
        status_filter = ttk.Combobox(search_frame, textvariable=self.status_filter_var,
                                   values=["All", "Active", "Inactive"], state="readonly", width=10)
        status_filter.set("Active")
        status_filter.pack(side=tk.LEFT, padx=(5, 10))
        
        # Employee list
        list_container = ttk.Frame(self.list_frame)
        list_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Treeview
        columns = ('ID', 'Name', 'Nationality', 'Job Type', 'Salary', 'Start Date', 'Status')
        self.employees_tree = ttk.Treeview(list_container, columns=columns, show='headings', height=15)
        
        # Configure columns
        column_widths = {'ID': 50, 'Name': 150, 'Nationality': 100, 'Job Type': 100, 
                        'Salary': 100, 'Start Date': 100, 'Status': 80}
        
        for col in columns:
            self.employees_tree.heading(col, text=col, command=lambda c=col: self.sort_employees(c))
            self.employees_tree.column(col, width=column_widths.get(col, 100))
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(list_container, orient=tk.VERTICAL, command=self.employees_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_container, orient=tk.HORIZONTAL, command=self.employees_tree.xview)
        self.employees_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.employees_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        list_container.grid_rowconfigure(0, weight=1)
        list_container.grid_columnconfigure(0, weight=1)
        
        # Buttons frame
        buttons_frame = ttk.Frame(self.list_frame)
        buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(buttons_frame, text="Add Employee", command=self.new_employee).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="Edit Employee", command=self.edit_employee).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="View Details", command=self.view_employee_details).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Deactivate", command=self.deactivate_employee).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Export to Excel", command=self.export_employees).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Refresh", command=self.load_employees).pack(side=tk.RIGHT)
        
        # Bind double-click to edit
        self.employees_tree.bind('<Double-1>', lambda e: self.edit_employee())
    
    def create_form_tab(self):
        """Create add/edit employee form tab"""
        # Main form frame with scrollbar
        canvas = tk.Canvas(self.form_frame)
        scrollbar = ttk.Scrollbar(self.form_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Form fields
        form_container = ttk.LabelFrame(scrollable_frame, text="Employee Information", padding="20")
        form_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Configure grid
        form_container.columnconfigure(1, weight=1)
        form_container.columnconfigure(3, weight=1)
        
        row = 0
        
        # Full Name
        ttk.Label(form_container, text="Full Name *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.name_var = tk.StringVar()
        name_entry = ttk.Entry(form_container, textvariable=self.name_var, width=30)
        name_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 20))
        
        # Nationality
        ttk.Label(form_container, text="Nationality *:").grid(row=row, column=2, sticky=tk.W, pady=5)
        self.nationality_var = tk.StringVar()
        nationality_combo = ttk.Combobox(form_container, textvariable=self.nationality_var,
                                       values=self.nationalities, width=25)
        nationality_combo.grid(row=row, column=3, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        row += 1
        
        # ID/Residence Number
        ttk.Label(form_container, text="ID/Residence Number *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.id_number_var = tk.StringVar()
        id_entry = ttk.Entry(form_container, textvariable=self.id_number_var, width=30)
        id_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 20))
        
        # Job Type
        ttk.Label(form_container, text="Job Type *:").grid(row=row, column=2, sticky=tk.W, pady=5)
        self.job_type_var = tk.StringVar()
        job_combo = ttk.Combobox(form_container, textvariable=self.job_type_var,
                               values=self.job_types, width=25)
        job_combo.grid(row=row, column=3, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        row += 1
        
        # Monthly Salary
        ttk.Label(form_container, text="Monthly Salary (QAR) *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.salary_var = tk.StringVar()
        salary_entry = ttk.Entry(form_container, textvariable=self.salary_var, width=30)
        salary_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 20))
        
        # Start Date
        ttk.Label(form_container, text="Start Date *:").grid(row=row, column=2, sticky=tk.W, pady=5)
        self.start_date_var = tk.StringVar()
        start_date_entry = ttk.Entry(form_container, textvariable=self.start_date_var, width=25)
        start_date_entry.grid(row=row, column=3, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        
        # Date format hint
        ttk.Label(form_container, text="(YYYY-MM-DD)", font=('Arial', 8)).grid(
            row=row+1, column=3, sticky=tk.W, padx=(5, 0))
        row += 2
        
        # Additional Notes
        ttk.Label(form_container, text="Additional Notes:").grid(row=row, column=0, sticky=(tk.W, tk.N), pady=5)
        self.notes_text = tk.Text(form_container, width=60, height=4, wrap=tk.WORD)
        self.notes_text.grid(row=row, column=1, columnspan=3, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        row += 1
        
        # Buttons frame
        buttons_frame = ttk.Frame(form_container)
        buttons_frame.grid(row=row, column=0, columnspan=4, pady=20)
        
        self.save_button = ttk.Button(buttons_frame, text="Save Employee", command=self.save_employee)
        self.save_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(buttons_frame, text="Clear Form", command=self.clear_form).pack(side=tk.LEFT, padx=10)
        ttk.Button(buttons_frame, text="Cancel", command=self.cancel_edit).pack(side=tk.LEFT, padx=10)
        
        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Current employee being edited
        self.current_employee_id = None
    
    def load_employees(self):
        """Load employees into the treeview"""
        # Clear existing items
        for item in self.employees_tree.get_children():
            self.employees_tree.delete(item)
        
        # Get filter values
        status_filter = self.status_filter_var.get() if hasattr(self, 'status_filter_var') else "Active"
        active_only = status_filter != "Inactive"
        
        # Load employees
        employees = self.employee_model.get_all_employees(active_only=(status_filter == "Active"))
        
        for emp in employees:
            status = "Active" if emp['is_active'] else "Inactive"
            self.employees_tree.insert('', tk.END, values=(
                emp['id'],
                emp['full_name'],
                emp['nationality'],
                emp['job_type'],
                f"{emp['monthly_salary']:,.2f}",
                emp['start_date'],
                status
            ))
    
    def filter_employees(self, *args):
        """Filter employees based on search criteria"""
        search_text = self.search_var.get().lower()
        job_filter = self.job_filter_var.get()
        status_filter = self.status_filter_var.get()
        
        # Clear current items
        for item in self.employees_tree.get_children():
            self.employees_tree.delete(item)
        
        # Get all employees
        employees = self.employee_model.get_all_employees(active_only=False)
        
        for emp in employees:
            # Apply filters
            if search_text and search_text not in emp['full_name'].lower():
                continue
            
            if job_filter != "All" and emp['job_type'] != job_filter:
                continue
            
            emp_status = "Active" if emp['is_active'] else "Inactive"
            if status_filter != "All" and emp_status != status_filter:
                continue
            
            # Add to tree
            self.employees_tree.insert('', tk.END, values=(
                emp['id'],
                emp['full_name'],
                emp['nationality'],
                emp['job_type'],
                f"{emp['monthly_salary']:,.2f}",
                emp['start_date'],
                emp_status
            ))
    
    def sort_employees(self, column):
        """Sort employees by column"""
        # This would implement sorting functionality
        pass
    
    def new_employee(self):
        """Start adding a new employee"""
        self.current_employee_id = None
        self.clear_form()
        # Switch to form tab
        notebook = self.window.nametowidget(self.window.winfo_children()[0])
        notebook.select(1)  # Select form tab
    
    def edit_employee(self):
        """Edit selected employee"""
        selection = self.employees_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select an employee to edit")
            return
        
        # Get employee ID
        item = self.employees_tree.item(selection[0])
        employee_id = item['values'][0]
        
        # Load employee data
        employee = self.employee_model.get_employee_by_id(employee_id)
        if not employee:
            messagebox.showerror("Error", "Employee not found")
            return
        
        # Populate form
        self.current_employee_id = employee_id
        self.name_var.set(employee['full_name'])
        self.nationality_var.set(employee['nationality'])
        self.id_number_var.set(employee['id_residence_number'])
        self.job_type_var.set(employee['job_type'])
        self.salary_var.set(str(employee['monthly_salary']))
        self.start_date_var.set(employee['start_date'])
        
        # Clear and set notes
        self.notes_text.delete(1.0, tk.END)
        if employee.get('additional_notes'):
            self.notes_text.insert(1.0, employee['additional_notes'])
        
        # Switch to form tab
        notebook = self.window.nametowidget(self.window.winfo_children()[0])
        notebook.select(1)  # Select form tab
        
        # Update button text
        self.save_button.config(text="Update Employee")

    def save_employee(self):
        """Save employee data"""
        # Collect form data
        employee_data = {
            'full_name': self.name_var.get().strip(),
            'nationality': self.nationality_var.get().strip(),
            'id_residence_number': self.id_number_var.get().strip(),
            'job_type': self.job_type_var.get().strip(),
            'monthly_salary': self.salary_var.get().strip(),
            'start_date': self.start_date_var.get().strip(),
            'additional_notes': self.notes_text.get(1.0, tk.END).strip()
        }

        # Validate data
        errors = Validators.validate_employee_data(employee_data)
        if errors:
            messagebox.showerror("Validation Error", "\n".join(errors))
            return

        # Convert salary to float
        try:
            employee_data['monthly_salary'] = float(employee_data['monthly_salary'])
        except ValueError:
            messagebox.showerror("Error", "Invalid salary amount")
            return

        # Save employee
        try:
            if self.current_employee_id:
                # Update existing employee
                success = self.employee_model.update_employee(self.current_employee_id, employee_data)
                action = "updated"
            else:
                # Create new employee
                success = self.employee_model.create_employee(employee_data)
                action = "created"

            if success:
                messagebox.showinfo("Success", f"Employee {action} successfully")
                self.clear_form()
                self.load_employees()
                # Switch back to list tab
                notebook = self.window.nametowidget(self.window.winfo_children()[0])
                notebook.select(0)
            else:
                messagebox.showerror("Error", f"Failed to {action.replace('d', '')} employee. ID/Residence number may already exist.")

        except Exception as e:
            messagebox.showerror("Error", f"An error occurred: {str(e)}")

    def clear_form(self):
        """Clear the employee form"""
        self.current_employee_id = None
        self.name_var.set("")
        self.nationality_var.set("")
        self.id_number_var.set("")
        self.job_type_var.set("")
        self.salary_var.set("")
        self.start_date_var.set(date.today().strftime("%Y-%m-%d"))
        self.notes_text.delete(1.0, tk.END)
        self.save_button.config(text="Save Employee")

    def cancel_edit(self):
        """Cancel editing and switch to list tab"""
        self.clear_form()
        notebook = self.window.nametowidget(self.window.winfo_children()[0])
        notebook.select(0)

    def view_employee_details(self):
        """View detailed employee information"""
        selection = self.employees_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select an employee to view")
            return

        # Get employee ID
        item = self.employees_tree.item(selection[0])
        employee_id = item['values'][0]

        # Load employee data
        employee = self.employee_model.get_employee_by_id(employee_id)
        if not employee:
            messagebox.showerror("Error", "Employee not found")
            return

        # Create details window
        EmployeeDetailsWindow(self.window, employee)

    def deactivate_employee(self):
        """Deactivate selected employee"""
        selection = self.employees_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select an employee to deactivate")
            return

        # Get employee data
        item = self.employees_tree.item(selection[0])
        employee_id = item['values'][0]
        employee_name = item['values'][1]

        # Confirm deactivation
        if messagebox.askyesno("Confirm Deactivation",
                              f"Are you sure you want to deactivate {employee_name}?\n\n"
                              "This will mark them as inactive but preserve their records."):
            try:
                success = self.employee_model.deactivate_employee(employee_id)
                if success:
                    messagebox.showinfo("Success", "Employee deactivated successfully")
                    self.load_employees()
                else:
                    messagebox.showerror("Error", "Failed to deactivate employee")
            except Exception as e:
                messagebox.showerror("Error", f"An error occurred: {str(e)}")

    def export_employees(self):
        """Export employees to Excel"""
        try:
            from utils.helpers import DataExporter
            import pandas as pd
            from tkinter import filedialog

            # Get file path
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="Save Employee Data"
            )

            if not file_path:
                return

            # Get employees data
            employees = self.employee_model.get_all_employees(active_only=False)
            export_data = DataExporter.prepare_employee_data_for_export(employees)

            # Create DataFrame and save
            df = pd.DataFrame(export_data)
            df.to_excel(file_path, index=False, sheet_name="Employees")

            messagebox.showinfo("Success", f"Employee data exported to {file_path}")

        except ImportError:
            messagebox.showerror("Error", "pandas library is required for Excel export")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export data: {str(e)}")

class EmployeeDetailsWindow:
    """Window to display detailed employee information"""
    def __init__(self, parent, employee_data):
        self.employee = employee_data

        self.window = tk.Toplevel(parent)
        self.window.title(f"Employee Details - {employee_data['full_name']}")
        self.window.geometry("500x600")
        self.window.transient(parent)
        self.window.grab_set()

        self.create_widgets()

    def create_widgets(self):
        """Create the details display"""
        # Main frame
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = ttk.Label(main_frame, text=self.employee['full_name'],
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))

        # Details frame
        details_frame = ttk.LabelFrame(main_frame, text="Employee Information", padding="15")
        details_frame.pack(fill=tk.BOTH, expand=True)

        # Create detail rows
        details = [
            ("Employee ID:", self.employee['id']),
            ("Full Name:", self.employee['full_name']),
            ("Nationality:", self.employee['nationality']),
            ("ID/Residence Number:", self.employee['id_residence_number']),
            ("Job Type:", self.employee['job_type']),
            ("Monthly Salary:", f"{self.employee['monthly_salary']:,.2f} QAR"),
            ("Start Date:", self.employee['start_date']),
            ("End Date:", self.employee.get('end_date', 'N/A')),
            ("Status:", "Active" if self.employee['is_active'] else "Inactive"),
            ("Created:", self.employee.get('created_at', 'N/A')[:10]),
            ("Last Updated:", self.employee.get('updated_at', 'N/A')[:10])
        ]

        for i, (label, value) in enumerate(details):
            ttk.Label(details_frame, text=label, font=('Arial', 10, 'bold')).grid(
                row=i, column=0, sticky=tk.W, pady=2, padx=(0, 10))
            ttk.Label(details_frame, text=str(value)).grid(
                row=i, column=1, sticky=tk.W, pady=2)

        # Notes section
        if self.employee.get('additional_notes'):
            notes_frame = ttk.LabelFrame(main_frame, text="Additional Notes", padding="15")
            notes_frame.pack(fill=tk.X, pady=(10, 0))

            notes_text = tk.Text(notes_frame, height=4, wrap=tk.WORD, state=tk.DISABLED)
            notes_text.pack(fill=tk.X)
            notes_text.config(state=tk.NORMAL)
            notes_text.insert(1.0, self.employee['additional_notes'])
            notes_text.config(state=tk.DISABLED)

        # Buttons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(20, 0))

        ttk.Button(buttons_frame, text="Close", command=self.window.destroy).pack(side=tk.RIGHT)
        ttk.Button(buttons_frame, text="Print", command=self.print_details).pack(side=tk.RIGHT, padx=(0, 10))

    def print_details(self):
        """Print employee details"""
        messagebox.showinfo("Print", "Print functionality would be implemented here")
