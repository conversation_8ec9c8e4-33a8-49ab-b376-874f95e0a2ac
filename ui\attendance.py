import tkinter as tk
from tkinter import ttk, messagebox, simpledialog, filedialog
from datetime import datetime, date, timedelta
from typing import Dict, Any, Optional, List
import calendar
from utils.validators import Validators
from utils.helpers import DateHelper

class AttendanceManagementWindow:
    def __init__(self, parent, employee_model, attendance_model, leave_model, session_manager):
        self.employee_model = employee_model
        self.attendance_model = attendance_model
        self.leave_model = leave_model
        self.session_manager = session_manager
        self.parent = parent
        
        self.window = tk.Toplevel(parent)
        self.window.title("Attendance & Leave Management")
        self.window.geometry("1200x700")
        self.window.transient(parent)
        
        self.create_widgets()
        self.load_initial_data()
    
    def create_widgets(self):
        """Create the attendance management interface"""
        # Create notebook for tabs
        notebook = ttk.Notebook(self.window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Daily Attendance Tab
        self.attendance_frame = ttk.Frame(notebook)
        notebook.add(self.attendance_frame, text="Daily Attendance")
        self.create_attendance_tab()
        
        # Leave Management Tab
        self.leave_frame = ttk.Frame(notebook)
        notebook.add(self.leave_frame, text="Leave Management")
        self.create_leave_tab()
        
        # Attendance Reports Tab
        self.reports_frame = ttk.Frame(notebook)
        notebook.add(self.reports_frame, text="Attendance Reports")
        self.create_reports_tab()
    
    def create_attendance_tab(self):
        """Create daily attendance tracking tab"""
        # Date selection frame
        date_frame = ttk.LabelFrame(self.attendance_frame, text="Attendance Date", padding="10")
        date_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(date_frame, text="Date:").pack(side=tk.LEFT)
        self.attendance_date_var = tk.StringVar(value=date.today().strftime("%Y-%m-%d"))
        date_entry = ttk.Entry(date_frame, textvariable=self.attendance_date_var, width=12)
        date_entry.pack(side=tk.LEFT, padx=(5, 15))
        
        ttk.Button(date_frame, text="Load Attendance", command=self.load_attendance_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(date_frame, text="Mark All Present", command=self.mark_all_present).pack(side=tk.LEFT, padx=5)
        ttk.Button(date_frame, text="Save Attendance", command=self.save_attendance).pack(side=tk.LEFT, padx=5)
        
        # Attendance data frame
        data_frame = ttk.LabelFrame(self.attendance_frame, text="Employee Attendance", padding="5")
        data_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Treeview for attendance
        columns = ('ID', 'Employee', 'Job Type', 'Status', 'Check In', 'Check Out', 
                  'Hours Worked', 'Overtime', 'Notes')
        self.attendance_tree = ttk.Treeview(data_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        column_widths = {
            'ID': 50, 'Employee': 150, 'Job Type': 100, 'Status': 80,
            'Check In': 80, 'Check Out': 80, 'Hours Worked': 100,
            'Overtime': 80, 'Notes': 150
        }
        
        for col in columns:
            self.attendance_tree.heading(col, text=col)
            self.attendance_tree.column(col, width=column_widths.get(col, 100))
        
        # Scrollbars
        att_v_scrollbar = ttk.Scrollbar(data_frame, orient=tk.VERTICAL, command=self.attendance_tree.yview)
        att_h_scrollbar = ttk.Scrollbar(data_frame, orient=tk.HORIZONTAL, command=self.attendance_tree.xview)
        self.attendance_tree.configure(yscrollcommand=att_v_scrollbar.set, xscrollcommand=att_h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.attendance_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        att_v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        att_h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        data_frame.grid_rowconfigure(0, weight=1)
        data_frame.grid_columnconfigure(0, weight=1)
        
        # Action buttons
        actions_frame = ttk.Frame(self.attendance_frame)
        actions_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(actions_frame, text="Edit Record", command=self.edit_attendance_record).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(actions_frame, text="Mark Absent", command=self.mark_absent).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_frame, text="Mark Present", command=self.mark_present).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_frame, text="Export", command=self.export_attendance).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_frame, text="Refresh", command=self.load_attendance_data).pack(side=tk.RIGHT)
        
        # Bind double-click to edit
        self.attendance_tree.bind('<Double-1>', lambda e: self.edit_attendance_record())
    
    def create_leave_tab(self):
        """Create leave management tab"""
        # Leave request form frame
        form_frame = ttk.LabelFrame(self.leave_frame, text="Leave Request", padding="10")
        form_frame.pack(fill=tk.X, padx=10, pady=5)
        
        form_frame.columnconfigure(1, weight=1)
        form_frame.columnconfigure(3, weight=1)
        
        # Employee selection
        ttk.Label(form_frame, text="Employee:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5), pady=5)
        self.leave_employee_var = tk.StringVar()
        self.leave_employee_combo = ttk.Combobox(form_frame, textvariable=self.leave_employee_var,
                                                state="readonly", width=25)
        self.leave_employee_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        
        # Leave type
        ttk.Label(form_frame, text="Leave Type:").grid(row=0, column=2, sticky=tk.W, padx=(15, 5), pady=5)
        self.leave_type_var = tk.StringVar()
        leave_type_combo = ttk.Combobox(form_frame, textvariable=self.leave_type_var,
                                       values=["Annual Leave", "Sick Leave", "Emergency Leave", 
                                              "Maternity Leave", "Unpaid Leave"], 
                                       state="readonly", width=15)
        leave_type_combo.grid(row=0, column=3, sticky=(tk.W, tk.E), padx=5, pady=5)
        
        # Start date
        ttk.Label(form_frame, text="Start Date:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=5)
        self.leave_start_var = tk.StringVar()
        start_entry = ttk.Entry(form_frame, textvariable=self.leave_start_var, width=12)
        start_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # End date
        ttk.Label(form_frame, text="End Date:").grid(row=1, column=2, sticky=tk.W, padx=(15, 5), pady=5)
        self.leave_end_var = tk.StringVar()
        end_entry = ttk.Entry(form_frame, textvariable=self.leave_end_var, width=12)
        end_entry.grid(row=1, column=3, sticky=tk.W, padx=5, pady=5)
        
        # Reason
        ttk.Label(form_frame, text="Reason:").grid(row=2, column=0, sticky=tk.W, padx=(0, 5), pady=5)
        self.leave_reason_var = tk.StringVar()
        reason_entry = ttk.Entry(form_frame, textvariable=self.leave_reason_var, width=50)
        reason_entry.grid(row=2, column=1, columnspan=3, sticky=(tk.W, tk.E), padx=5, pady=5)
        
        # Buttons
        button_frame = ttk.Frame(form_frame)
        button_frame.grid(row=3, column=0, columnspan=4, pady=(10, 0))
        
        ttk.Button(button_frame, text="Submit Request", command=self.submit_leave_request).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Clear Form", command=self.clear_leave_form).pack(side=tk.LEFT, padx=5)
        
        # Leave requests list frame
        list_frame = ttk.LabelFrame(self.leave_frame, text="Leave Requests", padding="5")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Treeview for leave requests
        leave_columns = ('ID', 'Employee', 'Type', 'Start Date', 'End Date', 'Days', 
                        'Reason', 'Status', 'Approved By', 'Actions')
        self.leave_tree = ttk.Treeview(list_frame, columns=leave_columns, show='headings', height=12)
        
        # Configure leave columns
        leave_widths = {
            'ID': 50, 'Employee': 120, 'Type': 100, 'Start Date': 80, 'End Date': 80,
            'Days': 50, 'Reason': 150, 'Status': 80, 'Approved By': 100, 'Actions': 80
        }
        
        for col in leave_columns:
            self.leave_tree.heading(col, text=col)
            self.leave_tree.column(col, width=leave_widths.get(col, 80))
        
        # Leave scrollbars
        leave_v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.leave_tree.yview)
        leave_h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.leave_tree.xview)
        self.leave_tree.configure(yscrollcommand=leave_v_scrollbar.set, xscrollcommand=leave_h_scrollbar.set)
        
        # Pack leave treeview and scrollbars
        self.leave_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        leave_v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        leave_h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        # Leave action buttons
        leave_actions_frame = ttk.Frame(self.leave_frame)
        leave_actions_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(leave_actions_frame, text="Approve", command=self.approve_leave).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(leave_actions_frame, text="Reject", command=self.reject_leave).pack(side=tk.LEFT, padx=5)
        ttk.Button(leave_actions_frame, text="Edit Request", command=self.edit_leave_request).pack(side=tk.LEFT, padx=5)
        ttk.Button(leave_actions_frame, text="Delete Request", command=self.delete_leave_request).pack(side=tk.LEFT, padx=5)
        ttk.Button(leave_actions_frame, text="Export", command=self.export_leave_requests).pack(side=tk.LEFT, padx=5)
        ttk.Button(leave_actions_frame, text="Refresh", command=self.load_leave_requests).pack(side=tk.RIGHT)
        
        # Bind double-click to edit
        self.leave_tree.bind('<Double-1>', lambda e: self.edit_leave_request())
    
    def create_reports_tab(self):
        """Create attendance reports tab"""
        # Report filters frame
        filter_frame = ttk.LabelFrame(self.reports_frame, text="Report Filters", padding="10")
        filter_frame.pack(fill=tk.X, padx=10, pady=5)
        
        filter_frame.columnconfigure(1, weight=1)
        filter_frame.columnconfigure(3, weight=1)
        
        # Employee filter
        ttk.Label(filter_frame, text="Employee:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.report_employee_var = tk.StringVar()
        self.report_employee_combo = ttk.Combobox(filter_frame, textvariable=self.report_employee_var,
                                                 state="readonly", width=25)
        self.report_employee_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5)
        
        # Report type
        ttk.Label(filter_frame, text="Report Type:").grid(row=0, column=2, sticky=tk.W, padx=(15, 5))
        self.report_type_var = tk.StringVar()
        report_type_combo = ttk.Combobox(filter_frame, textvariable=self.report_type_var,
                                        values=["Monthly Summary", "Daily Details", "Leave Summary", 
                                               "Overtime Report", "Absence Report"],
                                        state="readonly", width=15)
        report_type_combo.set("Monthly Summary")
        report_type_combo.grid(row=0, column=3, sticky=(tk.W, tk.E), padx=5)
        
        # Date range
        ttk.Label(filter_frame, text="Month:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.report_month_var = tk.StringVar()
        month_combo = ttk.Combobox(filter_frame, textvariable=self.report_month_var,
                                  values=[f"{i:02d} - {calendar.month_name[i]}" for i in range(1, 13)],
                                  state="readonly", width=15)
        month_combo.grid(row=1, column=1, sticky=tk.W, padx=5, pady=(5, 0))
        
        ttk.Label(filter_frame, text="Year:").grid(row=1, column=2, sticky=tk.W, padx=(15, 5), pady=(5, 0))
        self.report_year_var = tk.StringVar()
        current_year = datetime.now().year
        year_combo = ttk.Combobox(filter_frame, textvariable=self.report_year_var,
                                 values=[str(year) for year in range(current_year-2, current_year+1)],
                                 state="readonly", width=10)
        year_combo.grid(row=1, column=3, sticky=tk.W, padx=5, pady=(5, 0))
        
        # Generate button
        ttk.Button(filter_frame, text="Generate Report", command=self.generate_attendance_report).grid(
            row=2, column=0, columnspan=4, pady=(10, 0))
        
        # Report display frame
        display_frame = ttk.LabelFrame(self.reports_frame, text="Report", padding="10")
        display_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Text widget for report display
        self.report_text = tk.Text(display_frame, wrap=tk.WORD, font=('Courier', 10))
        report_scrollbar = ttk.Scrollbar(display_frame, orient=tk.VERTICAL, command=self.report_text.yview)
        self.report_text.configure(yscrollcommand=report_scrollbar.set)
        
        self.report_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        report_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Report action buttons
        report_actions_frame = ttk.Frame(self.reports_frame)
        report_actions_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(report_actions_frame, text="Export Report", command=self.export_report).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(report_actions_frame, text="Print Report", command=self.print_report).pack(side=tk.LEFT, padx=5)
        ttk.Button(report_actions_frame, text="Clear", command=self.clear_report).pack(side=tk.RIGHT)
    
    def load_initial_data(self):
        """Load initial data for dropdowns"""
        # Load employees
        employees = self.employee_model.get_all_employees(active_only=True)
        employee_list = [emp['full_name'] for emp in employees]
        
        # Set employee dropdowns
        self.leave_employee_combo['values'] = employee_list
        
        report_employee_list = ["All"] + employee_list
        self.report_employee_combo['values'] = report_employee_list
        self.report_employee_combo.set("All")
        
        # Set default dates
        now = datetime.now()
        self.report_month_var.set(f"{now.month:02d} - {calendar.month_name[now.month]}")
        self.report_year_var.set(str(now.year))
        
        # Load initial data
        self.load_attendance_data()
        self.load_leave_requests()
    
    def load_attendance_data(self):
        """Load attendance data for selected date"""
        if not self.attendance_date_var.get():
            messagebox.showwarning("Missing Date", "Please select a date")
            return
        
        # Clear existing data
        for item in self.attendance_tree.get_children():
            self.attendance_tree.delete(item)
        
        # Get employees and attendance records
        employees = self.employee_model.get_all_employees(active_only=True)
        attendance_date = self.attendance_date_var.get()
        
        for employee in employees:
            # Get attendance record for this date
            attendance = self.attendance_model.get_attendance_record(employee['id'], attendance_date)
            
            if attendance:
                status = attendance['status']
                check_in = attendance.get('check_in_time', '')
                check_out = attendance.get('check_out_time', '')
                hours_worked = attendance.get('hours_worked', 0)
                overtime = attendance.get('overtime_hours', 0)
                notes = attendance.get('notes', '')
            else:
                status = 'Not Marked'
                check_in = check_out = ''
                hours_worked = overtime = 0
                notes = ''
            
            self.attendance_tree.insert('', tk.END, values=(
                employee['id'],
                employee['full_name'],
                employee.get('job_type', ''),
                status,
                check_in,
                check_out,
                f"{hours_worked:.1f}",
                f"{overtime:.1f}",
                notes
            ))

    def mark_all_present(self):
        """Mark all employees as present for the selected date"""
        if not messagebox.askyesno("Confirm", "Mark all employees as present for this date?"):
            return

        attendance_date = self.attendance_date_var.get()
        if not attendance_date:
            messagebox.showwarning("Missing Date", "Please select a date")
            return

        employees = self.employee_model.get_all_employees(active_only=True)
        marked_count = 0

        for employee in employees:
            attendance_data = {
                'employee_id': employee['id'],
                'attendance_date': attendance_date,
                'status': 'Present',
                'check_in_time': '08:00',
                'check_out_time': '17:00',
                'hours_worked': 8.0,
                'overtime_hours': 0.0,
                'notes': 'Marked present automatically'
            }

            if self.attendance_model.create_or_update_attendance(attendance_data):
                marked_count += 1

        messagebox.showinfo("Success", f"Marked {marked_count} employees as present")
        self.load_attendance_data()

    def save_attendance(self):
        """Save all attendance records"""
        messagebox.showinfo("Save", "Attendance saving functionality would be implemented here")

    def edit_attendance_record(self):
        """Edit selected attendance record"""
        selection = self.attendance_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select an attendance record")
            return

        item = self.attendance_tree.item(selection[0])
        employee_id = item['values'][0]
        employee_name = item['values'][1]

        AttendanceEditDialog(self.window, employee_id, employee_name,
                           self.attendance_date_var.get(), self.attendance_model,
                           self.load_attendance_data)

    def mark_absent(self):
        """Mark selected employee as absent"""
        selection = self.attendance_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select an employee")
            return

        item = self.attendance_tree.item(selection[0])
        employee_id = item['values'][0]
        employee_name = item['values'][1]

        if messagebox.askyesno("Confirm", f"Mark {employee_name} as absent?"):
            attendance_data = {
                'employee_id': employee_id,
                'attendance_date': self.attendance_date_var.get(),
                'status': 'Absent',
                'notes': 'Marked absent manually'
            }

            if self.attendance_model.create_or_update_attendance(attendance_data):
                messagebox.showinfo("Success", f"{employee_name} marked as absent")
                self.load_attendance_data()
            else:
                messagebox.showerror("Error", "Failed to update attendance")

    def mark_present(self):
        """Mark selected employee as present"""
        selection = self.attendance_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select an employee")
            return

        item = self.attendance_tree.item(selection[0])
        employee_id = item['values'][0]
        employee_name = item['values'][1]

        if messagebox.askyesno("Confirm", f"Mark {employee_name} as present?"):
            attendance_data = {
                'employee_id': employee_id,
                'attendance_date': self.attendance_date_var.get(),
                'status': 'Present',
                'check_in_time': '08:00',
                'check_out_time': '17:00',
                'hours_worked': 8.0,
                'notes': 'Marked present manually'
            }

            if self.attendance_model.create_or_update_attendance(attendance_data):
                messagebox.showinfo("Success", f"{employee_name} marked as present")
                self.load_attendance_data()
            else:
                messagebox.showerror("Error", "Failed to update attendance")

    def export_attendance(self):
        """Export attendance data to Excel"""
        try:
            import pandas as pd
            from tkinter import filedialog

            # Get file path
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="Export Attendance Data",
                initialname=f"attendance_{self.attendance_date_var.get()}.xlsx"
            )

            if not file_path:
                return

            # Get attendance data
            data = []
            for child in self.attendance_tree.get_children():
                item = self.attendance_tree.item(child)
                values = item['values']
                data.append({
                    'Employee ID': values[0],
                    'Employee Name': values[1],
                    'Job Type': values[2],
                    'Status': values[3],
                    'Check In': values[4],
                    'Check Out': values[5],
                    'Hours Worked': values[6],
                    'Overtime': values[7],
                    'Notes': values[8]
                })

            # Create DataFrame and save
            df = pd.DataFrame(data)
            df.to_excel(file_path, index=False, sheet_name=f"Attendance_{self.attendance_date_var.get()}")

            messagebox.showinfo("Success", f"Attendance data exported to {file_path}")

        except ImportError:
            messagebox.showerror("Error", "pandas library is required for Excel export")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export data: {str(e)}")

    def load_leave_requests(self):
        """Load leave requests"""
        # Clear existing data
        for item in self.leave_tree.get_children():
            self.leave_tree.delete(item)

        # Get leave requests
        leave_requests = self.leave_model.get_all_leave_requests()

        for request in leave_requests:
            # Calculate days
            try:
                start_date = datetime.strptime(request['start_date'], '%Y-%m-%d').date()
                end_date = datetime.strptime(request['end_date'], '%Y-%m-%d').date()
                days = (end_date - start_date).days + 1
            except:
                days = 0

            self.leave_tree.insert('', tk.END, values=(
                request['id'],
                request['full_name'],
                request['leave_type'],
                request['start_date'],
                request['end_date'],
                days,
                request.get('reason', ''),
                request['status'],
                request.get('approved_by', ''),
                'Edit' if request['status'] == 'Pending' else 'View'
            ))

    def submit_leave_request(self):
        """Submit new leave request"""
        # Validate form
        if not all([self.leave_employee_var.get(), self.leave_type_var.get(),
                   self.leave_start_var.get(), self.leave_end_var.get()]):
            messagebox.showwarning("Missing Information", "Please fill all required fields")
            return

        # Validate dates
        try:
            start_date = datetime.strptime(self.leave_start_var.get(), '%Y-%m-%d').date()
            end_date = datetime.strptime(self.leave_end_var.get(), '%Y-%m-%d').date()

            if start_date > end_date:
                messagebox.showerror("Invalid Dates", "Start date cannot be after end date")
                return

            if start_date < date.today():
                messagebox.showwarning("Past Date", "Leave cannot be requested for past dates")
                return

        except ValueError:
            messagebox.showerror("Invalid Date", "Please enter dates in YYYY-MM-DD format")
            return

        # Get employee ID
        employee_name = self.leave_employee_var.get()
        employees = self.employee_model.get_all_employees()
        employee = next((emp for emp in employees if emp['full_name'] == employee_name), None)

        if not employee:
            messagebox.showerror("Error", "Employee not found")
            return

        # Create leave request
        leave_data = {
            'employee_id': employee['id'],
            'leave_type': self.leave_type_var.get(),
            'start_date': self.leave_start_var.get(),
            'end_date': self.leave_end_var.get(),
            'reason': self.leave_reason_var.get(),
            'requested_by': self.session_manager.current_user['id'] if self.session_manager.current_user else None
        }

        if self.leave_model.create_leave_request(leave_data):
            messagebox.showinfo("Success", "Leave request submitted successfully")
            self.clear_leave_form()
            self.load_leave_requests()
        else:
            messagebox.showerror("Error", "Failed to submit leave request")

    def clear_leave_form(self):
        """Clear leave request form"""
        self.leave_employee_var.set("")
        self.leave_type_var.set("")
        self.leave_start_var.set("")
        self.leave_end_var.set("")
        self.leave_reason_var.set("")

    def approve_leave(self):
        """Approve selected leave request"""
        selection = self.leave_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a leave request")
            return

        item = self.leave_tree.item(selection[0])
        request_id = item['values'][0]
        employee_name = item['values'][1]
        status = item['values'][7]

        if status != 'Pending':
            messagebox.showinfo("Already Processed", "This request has already been processed")
            return

        if not self.session_manager.has_permission('Supervisor'):
            messagebox.showerror("Access Denied", "Insufficient permissions to approve leave")
            return

        if messagebox.askyesno("Confirm", f"Approve leave request for {employee_name}?"):
            approval_data = {
                'status': 'Approved',
                'approved_by': self.session_manager.current_user['id'],
                'approved_date': date.today().strftime('%Y-%m-%d')
            }

            if self.leave_model.update_leave_status(request_id, approval_data):
                messagebox.showinfo("Success", "Leave request approved")
                self.load_leave_requests()
            else:
                messagebox.showerror("Error", "Failed to approve leave request")

    def reject_leave(self):
        """Reject selected leave request"""
        selection = self.leave_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a leave request")
            return

        item = self.leave_tree.item(selection[0])
        request_id = item['values'][0]
        employee_name = item['values'][1]
        status = item['values'][7]

        if status != 'Pending':
            messagebox.showinfo("Already Processed", "This request has already been processed")
            return

        if not self.session_manager.has_permission('Supervisor'):
            messagebox.showerror("Access Denied", "Insufficient permissions to reject leave")
            return

        # Get rejection reason
        reason = simpledialog.askstring("Rejection Reason",
                                        "Please enter reason for rejection:")
        if not reason:
            return

        if messagebox.askyesno("Confirm", f"Reject leave request for {employee_name}?"):
            rejection_data = {
                'status': 'Rejected',
                'approved_by': self.session_manager.current_user['id'],
                'approved_date': date.today().strftime('%Y-%m-%d'),
                'rejection_reason': reason
            }

            if self.leave_model.update_leave_status(request_id, rejection_data):
                messagebox.showinfo("Success", "Leave request rejected")
                self.load_leave_requests()
            else:
                messagebox.showerror("Error", "Failed to reject leave request")

    def edit_leave_request(self):
        """Edit selected leave request"""
        selection = self.leave_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a leave request")
            return

        item = self.leave_tree.item(selection[0])
        request_id = item['values'][0]
        status = item['values'][7]

        if status != 'Pending':
            messagebox.showinfo("Cannot Edit", "Only pending requests can be edited")
            return

        LeaveEditDialog(self.window, request_id, self.leave_model, self.load_leave_requests)

    def delete_leave_request(self):
        """Delete selected leave request"""
        selection = self.leave_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a leave request")
            return

        item = self.leave_tree.item(selection[0])
        request_id = item['values'][0]
        employee_name = item['values'][1]
        status = item['values'][7]

        if status != 'Pending':
            messagebox.showinfo("Cannot Delete", "Only pending requests can be deleted")
            return

        if messagebox.askyesno("Confirm Delete", f"Delete leave request for {employee_name}?"):
            if self.leave_model.delete_leave_request(request_id):
                messagebox.showinfo("Success", "Leave request deleted")
                self.load_leave_requests()
            else:
                messagebox.showerror("Error", "Failed to delete leave request")

    def export_leave_requests(self):
        """Export leave requests to Excel"""
        try:
            import pandas as pd
            from tkinter import filedialog

            # Get file path
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="Export Leave Requests",
                initialname="leave_requests.xlsx"
            )

            if not file_path:
                return

            # Get leave data
            data = []
            for child in self.leave_tree.get_children():
                item = self.leave_tree.item(child)
                values = item['values']
                data.append({
                    'Request ID': values[0],
                    'Employee': values[1],
                    'Leave Type': values[2],
                    'Start Date': values[3],
                    'End Date': values[4],
                    'Days': values[5],
                    'Reason': values[6],
                    'Status': values[7],
                    'Approved By': values[8]
                })

            # Create DataFrame and save
            df = pd.DataFrame(data)
            df.to_excel(file_path, index=False, sheet_name="Leave_Requests")

            messagebox.showinfo("Success", f"Leave requests exported to {file_path}")

        except ImportError:
            messagebox.showerror("Error", "pandas library is required for Excel export")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export data: {str(e)}")

    def generate_attendance_report(self):
        """Generate attendance report based on selected criteria"""
        if not all([self.report_month_var.get(), self.report_year_var.get()]):
            messagebox.showwarning("Missing Selection", "Please select month and year")
            return

        try:
            month = int(self.report_month_var.get().split(' - ')[0])
            year = int(self.report_year_var.get())
            employee_name = self.report_employee_var.get()
            report_type = self.report_type_var.get()

            # Generate report based on type
            if report_type == "Monthly Summary":
                report = self.generate_monthly_summary(employee_name, month, year)
            elif report_type == "Daily Details":
                report = self.generate_daily_details(employee_name, month, year)
            elif report_type == "Leave Summary":
                report = self.generate_leave_summary(employee_name, month, year)
            elif report_type == "Overtime Report":
                report = self.generate_overtime_report(employee_name, month, year)
            elif report_type == "Absence Report":
                report = self.generate_absence_report(employee_name, month, year)
            else:
                report = "Unknown report type selected"

            # Display report
            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(1.0, report)

        except ValueError:
            messagebox.showerror("Error", "Invalid month/year selection")

    def generate_monthly_summary(self, employee_name, month, year):
        """Generate monthly attendance summary"""
        report = f"""
MONTHLY ATTENDANCE SUMMARY
{'=' * 50}

Period: {calendar.month_name[month]} {year}
Employee: {employee_name if employee_name != "All" else "All Employees"}
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

SUMMARY:
{'-' * 50}

Note: This is a placeholder for the monthly attendance summary.
The actual implementation would:

1. Query attendance records for the specified period
2. Calculate working days, present days, absent days
3. Calculate total hours worked and overtime
4. Show attendance percentage
5. List leave days taken
6. Show any late arrivals or early departures

Sample data would be displayed here showing:
- Total Working Days: 22
- Days Present: 20
- Days Absent: 2
- Attendance Rate: 90.9%
- Total Hours Worked: 160.0
- Overtime Hours: 8.0
- Leave Days: 1
"""
        return report

    def generate_daily_details(self, employee_name, month, year):
        """Generate daily attendance details"""
        report = f"""
DAILY ATTENDANCE DETAILS
{'=' * 50}

Period: {calendar.month_name[month]} {year}
Employee: {employee_name if employee_name != "All" else "All Employees"}

DAILY BREAKDOWN:
{'-' * 50}

Date       | Status  | Check In | Check Out | Hours | Overtime | Notes
-----------|---------|----------|-----------|-------|----------|-------
2024-01-01 | Present | 08:00    | 17:00     | 8.0   | 0.0      |
2024-01-02 | Present | 08:15    | 17:00     | 7.75  | 0.0      | Late
2024-01-03 | Absent  | -        | -         | 0.0   | 0.0      | Sick
...

Note: This would show actual daily records from the database.
"""
        return report

    def generate_leave_summary(self, employee_name, month, year):
        """Generate leave summary report"""
        report = f"""
LEAVE SUMMARY REPORT
{'=' * 50}

Period: {calendar.month_name[month]} {year}
Employee: {employee_name if employee_name != "All" else "All Employees"}

LEAVE BREAKDOWN:
{'-' * 50}

Leave Type     | Days Taken | Status   | Dates
---------------|------------|----------|------------------
Annual Leave   | 2          | Approved | 2024-01-15 to 2024-01-16
Sick Leave     | 1          | Approved | 2024-01-03
Emergency      | 0          | -        | -

Total Leave Days: 3
Remaining Annual Leave: 18 days

Note: This would show actual leave records from the database.
"""
        return report

    def generate_overtime_report(self, employee_name, month, year):
        """Generate overtime report"""
        report = f"""
OVERTIME REPORT
{'=' * 50}

Period: {calendar.month_name[month]} {year}
Employee: {employee_name if employee_name != "All" else "All Employees"}

OVERTIME SUMMARY:
{'-' * 50}

Date       | Regular Hours | Overtime Hours | Total Hours | Rate | Amount
-----------|---------------|----------------|-------------|------|--------
2024-01-05 | 8.0          | 2.0            | 10.0        | 1.5x | 150.00
2024-01-12 | 8.0          | 3.0            | 11.0        | 1.5x | 225.00
2024-01-19 | 8.0          | 1.5            | 9.5         | 1.5x | 112.50

Total Overtime Hours: 6.5
Total Overtime Amount: 487.50 QAR

Note: This would calculate actual overtime from attendance records.
"""
        return report

    def generate_absence_report(self, employee_name, month, year):
        """Generate absence report"""
        report = f"""
ABSENCE REPORT
{'=' * 50}

Period: {calendar.month_name[month]} {year}
Employee: {employee_name if employee_name != "All" else "All Employees"}

ABSENCE SUMMARY:
{'-' * 50}

Date       | Reason        | Type      | Approved | Salary Impact
-----------|---------------|-----------|----------|---------------
2024-01-03 | Sick          | Medical   | Yes      | No deduction
2024-01-10 | Personal      | Unpaid    | Yes      | 1 day deduction
2024-01-25 | Emergency     | Emergency | Yes      | No deduction

Total Absence Days: 3
Unpaid Absence Days: 1
Salary Deduction: 166.67 QAR (1 day)

Note: This would show actual absence records and calculate deductions.
"""
        return report

    def export_report(self):
        """Export current report to file"""
        content = self.report_text.get(1.0, tk.END)
        if not content.strip():
            messagebox.showwarning("No Content", "No report to export")
            return

        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            title="Export Attendance Report"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("Success", f"Report exported to {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export report: {str(e)}")

    def print_report(self):
        """Print current report"""
        messagebox.showinfo("Print", "Report printing would be implemented here")

    def clear_report(self):
        """Clear report display"""
        self.report_text.delete(1.0, tk.END)

class AttendanceEditDialog:
    """Dialog for editing attendance records"""
    def __init__(self, parent, employee_id, employee_name, attendance_date, attendance_model, refresh_callback):
        self.employee_id = employee_id
        self.employee_name = employee_name
        self.attendance_date = attendance_date
        self.attendance_model = attendance_model
        self.refresh_callback = refresh_callback

        # Load existing record
        self.record = attendance_model.get_attendance_record(employee_id, attendance_date)

        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"Edit Attendance - {employee_name}")
        self.dialog.geometry("400x350")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.create_widgets()

    def create_widgets(self):
        """Create edit dialog widgets"""
        # Main frame
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Employee info
        info_frame = ttk.LabelFrame(main_frame, text="Employee Information", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(info_frame, text=f"Employee: {self.employee_name}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"Date: {self.attendance_date}").pack(anchor=tk.W)

        # Attendance details
        details_frame = ttk.LabelFrame(main_frame, text="Attendance Details", padding="10")
        details_frame.pack(fill=tk.X, pady=(0, 10))

        details_frame.columnconfigure(1, weight=1)

        # Status
        ttk.Label(details_frame, text="Status:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.status_var = tk.StringVar()
        status_combo = ttk.Combobox(details_frame, textvariable=self.status_var,
                                   values=["Present", "Absent", "Late", "Half Day"], state="readonly")
        status_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # Check in time
        ttk.Label(details_frame, text="Check In:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.check_in_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.check_in_var).grid(
            row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # Check out time
        ttk.Label(details_frame, text="Check Out:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.check_out_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.check_out_var).grid(
            row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # Hours worked
        ttk.Label(details_frame, text="Hours Worked:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.hours_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.hours_var).grid(
            row=3, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # Overtime hours
        ttk.Label(details_frame, text="Overtime Hours:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.overtime_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.overtime_var).grid(
            row=4, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # Notes
        ttk.Label(details_frame, text="Notes:").grid(row=5, column=0, sticky=tk.W, pady=5)
        self.notes_var = tk.StringVar()
        ttk.Entry(details_frame, textvariable=self.notes_var).grid(
            row=5, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # Load existing data
        if self.record:
            self.status_var.set(self.record.get('status', 'Present'))
            self.check_in_var.set(self.record.get('check_in_time', ''))
            self.check_out_var.set(self.record.get('check_out_time', ''))
            self.hours_var.set(str(self.record.get('hours_worked', 0)))
            self.overtime_var.set(str(self.record.get('overtime_hours', 0)))
            self.notes_var.set(self.record.get('notes', ''))
        else:
            self.status_var.set('Present')
            self.hours_var.set('8.0')
            self.overtime_var.set('0.0')

        # Buttons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(buttons_frame, text="Save", command=self.save_changes).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(buttons_frame, text="Cancel", command=self.dialog.destroy).pack(side=tk.RIGHT)

    def save_changes(self):
        """Save attendance changes"""
        try:
            attendance_data = {
                'employee_id': self.employee_id,
                'attendance_date': self.attendance_date,
                'status': self.status_var.get(),
                'check_in_time': self.check_in_var.get(),
                'check_out_time': self.check_out_var.get(),
                'hours_worked': float(self.hours_var.get() or 0),
                'overtime_hours': float(self.overtime_var.get() or 0),
                'notes': self.notes_var.get()
            }

            if self.attendance_model.create_or_update_attendance(attendance_data):
                messagebox.showinfo("Success", "Attendance record updated successfully")
                self.dialog.destroy()
                if self.refresh_callback:
                    self.refresh_callback()
            else:
                messagebox.showerror("Error", "Failed to update attendance record")

        except ValueError:
            messagebox.showerror("Error", "Please enter valid numeric values for hours")
        except Exception as e:
            messagebox.showerror("Error", f"An error occurred: {str(e)}")

class LeaveEditDialog:
    """Dialog for editing leave requests"""
    def __init__(self, parent, request_id, leave_model, refresh_callback):
        self.request_id = request_id
        self.leave_model = leave_model
        self.refresh_callback = refresh_callback

        # Load existing request
        self.request = leave_model.get_leave_request_by_id(request_id)
        if not self.request:
            messagebox.showerror("Error", "Leave request not found")
            return

        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Edit Leave Request")
        self.dialog.geometry("400x300")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.create_widgets()

    def create_widgets(self):
        """Create edit dialog widgets"""
        # Main frame
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Request info
        info_frame = ttk.LabelFrame(main_frame, text="Leave Request", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))

        info_frame.columnconfigure(1, weight=1)

        # Leave type
        ttk.Label(info_frame, text="Leave Type:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.leave_type_var = tk.StringVar(value=self.request.get('leave_type', ''))
        leave_type_combo = ttk.Combobox(info_frame, textvariable=self.leave_type_var,
                                       values=["Annual Leave", "Sick Leave", "Emergency Leave",
                                              "Maternity Leave", "Unpaid Leave"], state="readonly")
        leave_type_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # Start date
        ttk.Label(info_frame, text="Start Date:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.start_date_var = tk.StringVar(value=self.request.get('start_date', ''))
        ttk.Entry(info_frame, textvariable=self.start_date_var).grid(
            row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # End date
        ttk.Label(info_frame, text="End Date:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.end_date_var = tk.StringVar(value=self.request.get('end_date', ''))
        ttk.Entry(info_frame, textvariable=self.end_date_var).grid(
            row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # Reason
        ttk.Label(info_frame, text="Reason:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.reason_var = tk.StringVar(value=self.request.get('reason', ''))
        ttk.Entry(info_frame, textvariable=self.reason_var).grid(
            row=3, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # Buttons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(buttons_frame, text="Save", command=self.save_changes).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(buttons_frame, text="Cancel", command=self.dialog.destroy).pack(side=tk.RIGHT)

    def save_changes(self):
        """Save leave request changes"""
        # Validate dates
        try:
            start_date = datetime.strptime(self.start_date_var.get(), '%Y-%m-%d').date()
            end_date = datetime.strptime(self.end_date_var.get(), '%Y-%m-%d').date()

            if start_date > end_date:
                messagebox.showerror("Invalid Dates", "Start date cannot be after end date")
                return

        except ValueError:
            messagebox.showerror("Invalid Date", "Please enter dates in YYYY-MM-DD format")
            return

        # Update request
        update_data = {
            'leave_type': self.leave_type_var.get(),
            'start_date': self.start_date_var.get(),
            'end_date': self.end_date_var.get(),
            'reason': self.reason_var.get()
        }

        if self.leave_model.update_leave_request(self.request_id, update_data):
            messagebox.showinfo("Success", "Leave request updated successfully")
            self.dialog.destroy()
            if self.refresh_callback:
                self.refresh_callback()
        else:
            messagebox.showerror("Error", "Failed to update leave request")
