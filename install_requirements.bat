@echo off
chcp 65001 >nul
title تثبيت المكتبات المطلوبة

echo ========================================
echo تثبيت المكتبات المطلوبة للنظام
echo Installing Required Libraries
echo ========================================
echo.

echo 🔄 التحقق من Python...
python --version
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo يرجى تثبيت Python من python.org
    pause
    exit /b 1
)

echo.
echo 🔄 تحديث pip...
python -m pip install --upgrade pip

echo.
echo 🔄 تثبيت المكتبات...

echo تثبيت bcrypt...
pip install bcrypt

echo تثبيت pandas...
pip install pandas

echo تثبيت openpyxl...
pip install openpyxl

echo تثبيت reportlab...
pip install reportlab

echo تثبيت Pillow...
pip install Pillow

echo تثبيت python-dateutil...
pip install python-dateutil

echo.
echo ✓ تم تثبيت جميع المكتبات بنجاح
echo ✓ All libraries installed successfully
echo.
echo يمكنك الآن تشغيل النظام باستخدام run_system.bat
echo You can now run the system using run_system.bat
echo.
pause
